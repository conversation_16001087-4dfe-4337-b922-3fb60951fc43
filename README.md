# x_streamer

Real-Time X.com Tweet Streamer

## Description

This project fetches real-time tweets from X.com (formerly Twitter) using a combination of web scraping and SSE (Server-Sent Events). It extracts necessary API endpoints and tokens, resolves user IDs from handles, and streams tweets for specified users.

## Installation

1.  Clone the repository:

    ```bash
    git clone <repository_url>
    cd x_streamer
    ```

2.  Install the dependencies:

    ```bash
    pip install -r requirements.txt
    ```

## Usage

1.  **Bootstrap:** Run the `bootstrap.py` script to scrape the required API endpoint, bearer token, and operation names from X.com.

    ```bash
    python bootstrap.py
    ```

    This will output the scraped data to the console. This script does not require any modification.

2.  **Resolve User IDs:** Use the `resolver.py` script to resolve Twitter handles to user IDs. You will need to manually enter the graphql\_endpoint, bearer\_token, and user\_by\_screen\_name\_query from the bootstrap step.

    ```bash
    python resolver.py
    ```

    Modify the script with the correct values before running.

3.  **Stream Tweets:** Use the `streamer.py` script to stream tweets for specific user IDs. You will need to manually enter the graphql\_endpoint, bearer\_token, and user\_tweets\_stream from the bootstrap step.

    ```bash
    python streamer.py
    ```

    Modify the script with the correct values and user IDs before running.

## Dependencies

*   requests
*   re

## Error Handling and Reconnections

The scripts are designed to handle errors and reconnections gracefully. They include error handling for network requests, JSON decoding, and unexpected API responses. The `streamer.py` script attempts to maintain a persistent connection and handles reconnects automatically.
