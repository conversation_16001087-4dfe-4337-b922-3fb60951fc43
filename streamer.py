import json
from typing import List, Dict, Iterator
from urllib.parse import urlencode
import requests
import io

class Streamer:
    def __init__(self, graphql_endpoint: str, bearer_token: str, user_tweets_stream: str):
        self.graphql_endpoint = graphql_endpoint
        self.bearer_token = bearer_token
        self.user_tweets_stream = user_tweets_stream

    def stream_tweets(self, user_ids: List[str]) -> Iterator[Dict]:
        """
        Opens a persistent HTTP/2/SSE connection and yields every incoming tweet JSON.
        Handles reconnects, errors, and back-off gracefully.
        """
        for user_id in user_ids:
            variables = json.dumps({'userId': user_id})
            sse_url = f"{self.graphql_endpoint}/stream?operationName=UserTweetsStream&variables={urlencode({'userId': user_id})}"

            try:
                response = requests.get(sse_url, stream=True, headers={
                    "Authorization": f"Bearer {self.bearer_token}",
                    "Accept": "text/event-stream"
                })
                response.raise_for_status()

                for line in response.iter_lines():
                    if line:
                        try:
                            tweet = json.loads(line)
                            yield tweet
                        except json.JSONDecodeError:
                            print(f"Error decoding JSON: {line}")

            except Exception as e:
                print(f"An unexpected error occurred for user {user_id}: {e}")

if __name__ == "__main__":
    # Example Usage (replace with actual values)
    graphql_endpoint = "https://api.example.com/graphql"
    bearer_token = "AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOjz5dTvH3KnlX440%3D1hvWZpeW4pzj5xUVLVr8iFFCDsvxXhcDYopnAYqU9YO0Zfi9q0"
    user_tweets_stream = "1234567890"

    streamer = Streamer(graphql_endpoint, bearer_token, user_tweets_stream)
    user_ids = ["12345", "67890"]  # Replace with actual user IDs
    for tweet in streamer.stream_tweets(user_ids):
        print(json.dumps(tweet))
