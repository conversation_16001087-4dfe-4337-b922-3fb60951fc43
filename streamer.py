import json
from typing import List, Dict, Iterator, Set
import requests
import time
from datetime import datetime

class Streamer:
    def __init__(self, graphql_endpoint: str, bearer_token: str, user_tweets_stream: str):
        self.graphql_endpoint = graphql_endpoint
        self.bearer_token = bearer_token
        self.user_tweets_stream = user_tweets_stream
        self.seen_tweet_ids: Set[str] = set()

    def get_user_tweets(self, user_id: str, count: int = 20) -> List[Dict]:
        """
        Fetch recent tweets for a specific user using GraphQL API
        """
        variables = {
            "userId": user_id,
            "count": count,
            "includePromotedContent": False,
            "withQuickPromoteEligibilityTweetFields": True,
            "withVoice": True,
            "withV2Timeline": True
        }

        params = {
            "variables": json.dumps(variables),
            "features": json.dumps({
                "rweb_lists_timeline_redesign_enabled": True,
                "responsive_web_graphql_exclude_directive_enabled": True,
                "verified_phone_label_enabled": False,
                "creator_subscriptions_tweet_preview_api_enabled": True,
                "responsive_web_graphql_timeline_navigation_enabled": True,
                "responsive_web_graphql_skip_user_profile_image_extensions_enabled": False,
                "tweetypie_unmention_optimization_enabled": True,
                "responsive_web_edit_tweet_api_enabled": True,
                "graphql_is_translatable_rweb_tweet_is_translatable_enabled": True,
                "view_counts_everywhere_api_enabled": True,
                "longform_notetweets_consumption_enabled": True,
                "responsive_web_twitter_article_tweet_consumption_enabled": False,
                "tweet_awards_web_tipping_enabled": False,
                "freedom_of_speech_not_reach_fetch_enabled": True,
                "standardized_nudges_misinfo": True,
                "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": True,
                "longform_notetweets_rich_text_read_enabled": True,
                "longform_notetweets_inline_media_enabled": True,
                "responsive_web_media_download_video_enabled": False,
                "responsive_web_enhance_cards_enabled": False
            })
        }

        url = f"{self.graphql_endpoint}/{self.user_tweets_stream}"

        headers = {
            "Authorization": self.bearer_token,
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "x-twitter-auth-type": "OAuth2Session",
            "x-twitter-client-language": "en",
            "x-twitter-active-user": "yes",
            "x-csrf-token": "placeholder",  # You might need to extract this from cookies
        }

        try:
            response = requests.get(url, params=params, headers=headers, timeout=30)
            response.raise_for_status()

            data = response.json()
            tweets = []

            # Parse the GraphQL response to extract tweets
            if "data" in data and "user" in data["data"]:
                timeline = data["data"]["user"]["result"]["timeline_v2"]["timeline"]
                if "instructions" in timeline:
                    for instruction in timeline["instructions"]:
                        if instruction.get("type") == "TimelineAddEntries":
                            for entry in instruction.get("entries", []):
                                if entry.get("content", {}).get("entryType") == "TimelineTimelineItem":
                                    tweet_result = entry["content"]["itemContent"]["tweet_results"]["result"]
                                    if "legacy" in tweet_result:
                                        tweets.append(tweet_result["legacy"])

            return tweets

        except requests.exceptions.RequestException as e:
            print(f"Request error for user {user_id}: {e}")
            return []
        except json.JSONDecodeError as e:
            print(f"JSON decode error for user {user_id}: {e}")
            return []
        except Exception as e:
            print(f"Unexpected error for user {user_id}: {e}")
            return []

    def poll_tweets(self, user_ids: List[str], interval: int = 30) -> Iterator[Dict]:
        """
        Poll for new tweets at regular intervals (pseudo real-time streaming)
        """
        print(f"Starting to poll tweets for {len(user_ids)} users every {interval} seconds...")

        while True:
            for user_id in user_ids:
                print(f"Checking tweets for user {user_id}...")
                tweets = self.get_user_tweets(user_id)

                new_tweets = []
                for tweet in tweets:
                    tweet_id = tweet.get("id_str", "")
                    if tweet_id and tweet_id not in self.seen_tweet_ids:
                        self.seen_tweet_ids.add(tweet_id)
                        new_tweets.append(tweet)

                for tweet in new_tweets:
                    tweet["user_id"] = user_id  # Add user_id for context
                    tweet["fetched_at"] = datetime.now().isoformat()
                    yield tweet

                if new_tweets:
                    print(f"Found {len(new_tweets)} new tweets for user {user_id}")
                else:
                    print(f"No new tweets for user {user_id}")

            print(f"Waiting {interval} seconds before next poll...")
            time.sleep(interval)

if __name__ == "__main__":
    # Example Usage (replace with actual values from your test2.py output)
    graphql_endpoint = "https://api.twitter.com"
    bearer_token = "Bearer AAAAAAAAAAAAAAAAAAAAAMupswEAAAAANC5Yk%2FHGiZmGDRV3EhXMBO3uX08%3DEwAT9YySxXZXGrYScXeoKUaeyqXQFeNVWUW4SaZUvtegCUVjIi"
    user_tweets_stream = "hiTzPd4vKNScu8qzxdjM4g"

    streamer = Streamer(graphql_endpoint, bearer_token, user_tweets_stream)
    user_ids = ["12345", "67890"]  # Replace with actual user IDs from resolver.py

    # Use polling instead of streaming
    for tweet in streamer.poll_tweets(user_ids, interval=30):
        print(f"New tweet from user {tweet.get('user_id', 'unknown')}:")
        print(f"  Text: {tweet.get('full_text', tweet.get('text', 'No text'))}")
        print(f"  Created: {tweet.get('created_at', 'Unknown time')}")
        print(f"  Tweet ID: {tweet.get('id_str', 'Unknown ID')}")
        print("-" * 50)
