#!/usr/bin/env python3
"""
Browser-based X.com Tweet Streamer using Selenium
This approach uses a real browser to avoid detection
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time
import json
from datetime import datetime
from typing import Set, List, Dict

class BrowserStreamer:
    def __init__(self, headless: bool = False):
        self.seen_tweet_ids: Set[str] = set()
        self.driver = None
        self.headless = headless
        
    def setup_driver(self):
        """Setup Chrome driver with appropriate options"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
    def login(self, username: str, password: str) -> bool:
        """Login to X.com (you'll need valid credentials)"""
        try:
            print("🔄 Logging into X.com...")
            self.driver.get("https://x.com/login")
            
            # Wait for username field and enter username
            username_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "text"))
            )
            username_field.send_keys(username)
            
            # Click Next
            next_button = self.driver.find_element(By.XPATH, "//span[text()='Next']")
            next_button.click()
            
            # Wait for password field and enter password
            password_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "password"))
            )
            password_field.send_keys(password)
            
            # Click Login
            login_button = self.driver.find_element(By.XPATH, "//span[text()='Log in']")
            login_button.click()
            
            # Wait for successful login (check for home timeline)
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//nav[@role='navigation']"))
            )
            
            print("✅ Successfully logged in!")
            return True
            
        except Exception as e:
            print(f"❌ Login failed: {e}")
            return False
    
    def get_tweets_from_user_page(self, username: str) -> List[Dict]:
        """Get tweets from a user's profile page"""
        try:
            print(f"🔄 Checking tweets from @{username}...")
            self.driver.get(f"https://x.com/{username}")
            
            # Wait for tweets to load
            time.sleep(3)
            
            # Find tweet elements
            tweet_elements = self.driver.find_elements(By.XPATH, "//article[@data-testid='tweet']")
            
            new_tweets = []
            for tweet_element in tweet_elements:
                try:
                    # Extract tweet data
                    tweet_text_element = tweet_element.find_element(By.XPATH, ".//div[@data-testid='tweetText']")
                    tweet_text = tweet_text_element.text if tweet_text_element else "No text"
                    
                    # Try to get tweet ID from the link
                    tweet_link = tweet_element.find_element(By.XPATH, ".//a[contains(@href, '/status/')]")
                    tweet_url = tweet_link.get_attribute('href')
                    tweet_id = tweet_url.split('/status/')[-1].split('?')[0] if tweet_url else None
                    
                    # Get timestamp
                    time_element = tweet_element.find_element(By.XPATH, ".//time")
                    tweet_time = time_element.get_attribute('datetime') if time_element else None
                    
                    if tweet_id and tweet_id not in self.seen_tweet_ids:
                        self.seen_tweet_ids.add(tweet_id)
                        
                        tweet_data = {
                            'id': tweet_id,
                            'text': tweet_text,
                            'username': username,
                            'timestamp': tweet_time,
                            'url': tweet_url,
                            'fetched_at': datetime.now().isoformat()
                        }
                        
                        new_tweets.append(tweet_data)
                        
                except Exception as e:
                    print(f"  ⚠️ Error parsing tweet: {e}")
                    continue
            
            return new_tweets
            
        except Exception as e:
            print(f"❌ Error getting tweets from @{username}: {e}")
            return []
    
    def stream_users(self, usernames: List[str], interval: int = 60):
        """Stream tweets from multiple users"""
        print(f"🚀 Starting browser-based streaming for users: {usernames}")
        print(f"📊 Checking every {interval} seconds")
        print("Press Ctrl+C to stop")
        
        try:
            while True:
                for username in usernames:
                    new_tweets = self.get_tweets_from_user_page(username)
                    
                    if new_tweets:
                        print(f"\n📱 {len(new_tweets)} new tweet(s) from @{username}:")
                        for tweet in new_tweets:
                            print(f"  🐦 {tweet['text'][:100]}...")
                            print(f"     ⏰ {tweet['timestamp']}")
                            print(f"     🔗 {tweet['url']}")
                            print()
                    else:
                        print(f"  ℹ️ No new tweets from @{username}")
                
                print(f"⏳ Waiting {interval} seconds before next check...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n👋 Stopping streamer...")
        finally:
            if self.driver:
                self.driver.quit()
    
    def start_streaming(self, usernames: List[str], username: str = None, password: str = None, interval: int = 60):
        """Start the streaming process"""
        try:
            self.setup_driver()
            
            if username and password:
                if not self.login(username, password):
                    return
            else:
                print("⚠️ No login credentials provided. Some content may not be accessible.")
                print("🔄 Going to X.com without login...")
                self.driver.get("https://x.com")
                time.sleep(5)
            
            self.stream_users(usernames, interval)
            
        except Exception as e:
            print(f"❌ Streaming failed: {e}")
        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    # Example usage
    streamer = BrowserStreamer(headless=False)  # Set to True to run without GUI
    
    # Users to track
    users_to_track = ["elonmusk", "OpenAI", "sama"]
    
    # Option 1: Stream without login (limited access)
    streamer.start_streaming(users_to_track, interval=120)
    
    # Option 2: Stream with login (better access, but requires valid credentials)
    # streamer.start_streaming(
    #     users_to_track, 
    #     username="your_username", 
    #     password="your_password", 
    #     interval=60
    # )
