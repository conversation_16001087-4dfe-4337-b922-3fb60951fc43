import re
import requests
from typing import <PERSON><PERSON>

def scrape_bundle_data() -> <PERSON><PERSON>[str, str, str, str]:
    """
    Scrapes the X.com HTML, finds the main JS bundle URL, downloads it,
    and extracts the GraphQL endpoint, guest bearer token, and operation names.
    """
    try:
        response = requests.get("https://x.com")
        response.raise_for_status()
        script_tag_match = re.search(r'<script[^>]*src="([^"]*(main\.[^"]*\.js))"', response.text)

        if not script_tag_match:
            raise ValueError("Could not find main JS bundle URL")
        bundle_url = script_tag_match.group(1)

        bundle_response = requests.get(bundle_url)
        bundle_response.raise_for_status()
        bundle_text = bundle_response.text

        graphql_endpoint_match = re.search(r"GraphQL\\\",\\\"url\\\":\\\"(https://api-p[0-9]+\.x.com/graphql/[^\\\"]+)\\\"", bundle_text)
        bearer_token_match = re.search(r"guest_token_response.*?=\s*\"([A-Za-z0-9%]+)\"", bundle_text)
        user_by_screen_name_query_match = re.search(r"UserByScreenNameQuery.*?id:\"([A-Za-z0-9]+)\"", bundle_text)
        user_tweets_stream_match = re.search(r"UserTweetsStream.*?id:\"([A-Za-z0-9]+)\"", bundle_text)

        if not graphql_endpoint_match or not bearer_token_match or not user_by_screen_name_query_match or not user_tweets_stream_match:
            raise ValueError("Could not extract GraphQL endpoint, bearer token, or operation names")

        graphql_endpoint = graphql_endpoint_match.group(1)
        bearer_token = bearer_token_match.group(1)
        user_by_screen_name_query = user_by_screen_name_query_match.group(1)
        user_tweets_stream = user_tweets_stream_match.group(1)

        return graphql_endpoint, bearer_token, user_by_screen_name_query, user_tweets_stream

    except requests.exceptions.RequestException as e:
        raise Exception(f"Request error: {e}")
    except ValueError as e:
        raise Exception(f"Error extracting data: {e}")
    except Exception as e:
        raise Exception(f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    try:
        graphql_endpoint, bearer_token, user_by_screen_name_query, user_tweets_stream = scrape_bundle_data()
        print(f"GraphQL Endpoint: {graphql_endpoint}")
        print(f"Bearer Token: {bearer_token}")
        print(f"UserByScreenNameQuery: {user_by_screen_name_query}")
        print(f"UserTweetsStream: {user_tweets_stream}")
    except Exception as e:
        print(f"Error: {e}")
