import re
import requests
from typing import <PERSON><PERSON>, Optional
import time
import random

def scrape_bundle_data() -> <PERSON><PERSON>[str, str, str, str]:
    """
    Scrapes the X.com HTML, finds the main JS bundle URL, downloads it,
    and extracts the GraphQL endpoint, guest bearer token, and operation names.
    
    Raises:
        Exception: If any required data cannot be extracted
    """
    
    # Headers to mimic a real browser request
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
    }
    
    try:
        # Add a small delay to avoid being too aggressive
        time.sleep(random.uniform(1, 3))
        
        print("Fetching X.com homepage...")
        response = requests.get("https://x.com", headers=headers, timeout=30)
        response.raise_for_status()
        
        print("Searching for main JS bundle...")
        # Try multiple patterns for finding the main bundle
        bundle_patterns = [
            r'<script[^>]*src="([^"]*(main\.[^"]*\.js))"',
            r'<script[^>]*src="([^"]*main[^"]*\.js)"',
            r'"src":"([^"]*main[^"]*\.js)"'
        ]
        
        bundle_url = None
        for pattern in bundle_patterns:
            script_tag_match = re.search(pattern, response.text)
            if script_tag_match:
                bundle_url = script_tag_match.group(1)
                # Ensure the URL is absolute
                if bundle_url.startswith('//'):
                    bundle_url = 'https:' + bundle_url
                elif bundle_url.startswith('/'):
                    bundle_url = 'https://x.com' + bundle_url
                break

        if not bundle_url:
            raise ValueError("Could not find main JS bundle URL")
        
        print(f"Found bundle URL: {bundle_url}")
        
        # Add another delay before fetching the bundle
        time.sleep(random.uniform(1, 2))
        
        print("Fetching JS bundle...")
        bundle_response = requests.get(bundle_url, headers=headers, timeout=30)
        bundle_response.raise_for_status()
        bundle_text = bundle_response.text
        
        print("Extracting GraphQL data...")
        
        # Updated regex patterns for better matching
        graphql_patterns = [
            r"GraphQL\\\",\\\"url\\\":\\\"(https://api-p[0-9]+\.x.com/graphql/[^\\\"]+)\\\"",
            r"GraphQL.*?url.*?(https://api-p[0-9]+\.x\.com/graphql/[^\"\\]+)",
            r"(https://api-p[0-9]+\.x\.com/graphql/[^\"\\]+)"
        ]
        
        bearer_patterns = [
            r"guest_token_response.*?=\s*\"([A-Za-z0-9%]+)\"",
            r"guest.*?token.*?[\"']([A-Za-z0-9%]{20,})[\"']",
            r"Bearer\s+([A-Za-z0-9%]{20,})"
        ]
        
        query_patterns = [
            r"UserByScreenNameQuery.*?id[\"']?\s*:\s*[\"']([A-Za-z0-9]+)[\"']",
            r"UserByScreenName.*?[\"']([A-Za-z0-9]{20,})[\"']"
        ]
        
        tweets_patterns = [
            r"UserTweetsStream.*?id[\"']?\s*:\s*[\"']([A-Za-z0-9]+)[\"']",
            r"UserTweets.*?[\"']([A-Za-z0-9]{20,})[\"']"
        ]
        
        # Try to find matches using multiple patterns
        graphql_endpoint: Optional[str] = None
        for pattern in graphql_patterns:
            match = re.search(pattern, bundle_text)
            if match:
                graphql_endpoint = match.group(1)
                break
        
        bearer_token: Optional[str] = None  
        for pattern in bearer_patterns:
            match = re.search(pattern, bundle_text)
            if match:
                bearer_token = match.group(1)
                break
                
        user_by_screen_name_query: Optional[str] = None
        for pattern in query_patterns:
            match = re.search(pattern, bundle_text)
            if match:
                user_by_screen_name_query = match.group(1)
                break
                
        user_tweets_stream: Optional[str] = None
        for pattern in tweets_patterns:
            match = re.search(pattern, bundle_text)
            if match:
                user_tweets_stream = match.group(1)
                break

        # Check what we found
        found_items = []
        if graphql_endpoint:
            found_items.append("GraphQL endpoint")
        if bearer_token:
            found_items.append("Bearer token")
        if user_by_screen_name_query:
            found_items.append("UserByScreenNameQuery")
        if user_tweets_stream:
            found_items.append("UserTweetsStream")
            
        print(f"Found: {', '.join(found_items)}")
        
        if not all([graphql_endpoint, bearer_token, user_by_screen_name_query, user_tweets_stream]):
            missing = []
            if not graphql_endpoint:
                missing.append("GraphQL endpoint")
            if not bearer_token:
                missing.append("Bearer token")
            if not user_by_screen_name_query:
                missing.append("UserByScreenNameQuery")
            if not user_tweets_stream:
                missing.append("UserTweetsStream")
            
            print(f"Missing: {', '.join(missing)}")
            print("Bundle size:", len(bundle_text), "characters")
            
            # Optionally save bundle for manual inspection
            with open('bundle_debug.js', 'w', encoding='utf-8') as f:
                f.write(bundle_text[:10000])  # First 10k chars for inspection
            print("Saved first 10k characters to bundle_debug.js for inspection")
            
            raise ValueError(f"Could not extract all required data. Missing: {', '.join(missing)}")

        # At this point, all values are guaranteed to be strings, not None
        # Type assertion to satisfy Pylance
        assert graphql_endpoint is not None
        assert bearer_token is not None  
        assert user_by_screen_name_query is not None
        assert user_tweets_stream is not None
        
        return graphql_endpoint, bearer_token, user_by_screen_name_query, user_tweets_stream

    except requests.exceptions.RequestException as e:
        if hasattr(e, 'response') and e.response is not None:
            print(f"HTTP Status: {e.response.status_code}")
            print(f"Response headers: {dict(e.response.headers)}")
            if e.response.text:
                print(f"Response body (first 500 chars): {e.response.text[:500]}")
        raise Exception(f"Request error: {e}")
    except ValueError as e:
        raise Exception(f"Error extracting data: {e}")
    except Exception as e:
        raise Exception(f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    try:
        graphql_endpoint, bearer_token, user_by_screen_name_query, user_tweets_stream = scrape_bundle_data()
        print("\n" + "="*50)
        print("RESULTS:")
        print("="*50)
        print(f"GraphQL Endpoint: {graphql_endpoint}")
        print(f"Bearer Token: {bearer_token}")
        print(f"UserByScreenNameQuery: {user_by_screen_name_query}")
        print(f"UserTweetsStream: {user_tweets_stream}")
    except Exception as e:
        print(f"Error: {e}")