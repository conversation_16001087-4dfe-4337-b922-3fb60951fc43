#!/usr/bin/env python3
"""
DOM Observer Monitor - The most advanced approach!
Uses JavaScript MutationObserver to detect DOM changes in real-time
No polling needed - instant detection when new tweets appear!
"""

import time
import threading
import json
from datetime import datetime
from typing import Set, Dict, Optional, Callable, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class DOMObserverMonitor:
    def __init__(self, headless: bool = True):
        self.headless = headless
        self.monitoring_threads = {}
        self.callbacks = []

    def add_callback(self, callback: Callable[[Dict], None]):
        """Add a callback function to handle new tweets"""
        self.callbacks.append(callback)

    def setup_driver(self):
        """Setup Chrome driver"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")

        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        return driver

    def inject_mutation_observer(self, driver, username: str):
        """Inject JavaScript MutationObserver to detect new tweets in real-time"""
        observer_script = """
        // Create a global variable to store detected tweets
        window.detectedTweets = [];

        // Function to extract tweet data from an element
        function extractTweetData(tweetElement) {
            try {
                // Get tweet text
                const textElement = tweetElement.querySelector('[data-testid="tweetText"]');
                const text = textElement ? textElement.innerText : '[Media/Retweet]';

                // Get tweet URL and ID
                const linkElement = tweetElement.querySelector('a[href*="/status/"]');
                if (!linkElement) return null;

                const url = linkElement.href;
                const tweetId = url.split('/status/')[1]?.split('?')[0];
                if (!tweetId) return null;

                // Get timestamp
                const timeElement = tweetElement.querySelector('time');
                const timestamp = timeElement ? timeElement.getAttribute('datetime') : null;
                const displayTime = timeElement ? timeElement.innerText : 'Unknown';

                // Get engagement metrics
                const replyElement = tweetElement.querySelector('[data-testid="reply"] [data-testid="app-text-transition-container"]');
                const retweetElement = tweetElement.querySelector('[data-testid="retweet"] [data-testid="app-text-transition-container"]');
                const likeElement = tweetElement.querySelector('[data-testid="like"] [data-testid="app-text-transition-container"]');

                return {
                    id: tweetId,
                    text: text,
                    url: url,
                    timestamp: timestamp,
                    displayTime: displayTime,
                    replies: replyElement ? replyElement.innerText || '0' : '0',
                    retweets: retweetElement ? retweetElement.innerText || '0' : '0',
                    likes: likeElement ? likeElement.innerText || '0' : '0',
                    detectedAt: new Date().toISOString()
                };
            } catch (error) {
                console.log('Error extracting tweet data:', error);
                return null;
            }
        }

        // Set to track seen tweet IDs
        const seenTweetIds = new Set();

        // Function to process existing tweets (baseline)
        function processExistingTweets() {
            const existingTweets = document.querySelectorAll('article[data-testid="tweet"]');
            existingTweets.forEach(tweet => {
                const tweetData = extractTweetData(tweet);
                if (tweetData && tweetData.id) {
                    seenTweetIds.add(tweetData.id);
                }
            });
            console.log(`Baseline: ${seenTweetIds.size} existing tweets`);
        }

        // Process existing tweets first
        processExistingTweets();

        // Create MutationObserver to watch for new tweets
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // Check for added nodes
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if the added node is a tweet
                        let tweetElement = null;

                        if (node.matches && node.matches('article[data-testid="tweet"]')) {
                            tweetElement = node;
                        } else if (node.querySelector) {
                            tweetElement = node.querySelector('article[data-testid="tweet"]');
                        }

                        if (tweetElement) {
                            const tweetData = extractTweetData(tweetElement);

                            if (tweetData && tweetData.id && !seenTweetIds.has(tweetData.id)) {
                                seenTweetIds.add(tweetData.id);

                                // Add to detected tweets array
                                window.detectedTweets.push(tweetData);

                                console.log('🆕 NEW TWEET DETECTED:', tweetData.text.substring(0, 50) + '...');
                            }
                        }
                    }
                });
            });
        });

        // Start observing the timeline container
        const timelineContainer = document.querySelector('[data-testid="primaryColumn"]') || document.body;
        observer.observe(timelineContainer, {
            childList: true,
            subtree: true
        });

        console.log('🔍 MutationObserver started - watching for new tweets...');

        // Also scroll to top periodically to trigger loading
        setInterval(() => {
            window.scrollTo(0, 0);
        }, 5000); // Scroll to top every 5 seconds
        """

        driver.execute_script(observer_script)
        print(f"✅ Injected MutationObserver for @{username}")

    def monitor_user_with_observer(self, username: str, check_interval: int = 1):
        """Monitor using DOM MutationObserver - most efficient method"""
        print(f"🔄 Starting DOM Observer monitoring for @{username}")

        driver = None

        try:
            driver = self.setup_driver()

            # Navigate to user profile
            print(f"🌐 Loading https://x.com/{username}")
            driver.get(f"https://x.com/{username}")

            # Wait for timeline to load
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.XPATH, "//article[@data-testid='tweet']"))
            )

            print(f"✅ Loaded @{username}'s profile")

            # Inject the MutationObserver
            self.inject_mutation_observer(driver, username)

            print(f"🔍 Real-time monitoring active for @{username}")
            print(f"⚡ Using MutationObserver - instant detection!")

            # Monitoring loop - just check for detected tweets
            while username in self.monitoring_threads:
                try:
                    # Get detected tweets from JavaScript
                    detected_tweets = driver.execute_script("return window.detectedTweets || [];")

                    if detected_tweets:
                        # Clear the array after retrieving
                        driver.execute_script("window.detectedTweets = [];")

                        print(f"🆕 Processing {len(detected_tweets)} new tweet(s) from @{username}!")

                        for tweet_data in detected_tweets:
                            # Add username to tweet data
                            tweet_data['username'] = username

                            print(f"   📝 {tweet_data['text'][:80]}...")
                            print(f"   ⏰ {tweet_data['displayTime']} | 💬 {tweet_data['replies']} | 🔄 {tweet_data['retweets']} | ❤️ {tweet_data['likes']}")

                            # Call all registered callbacks
                            for callback in self.callbacks:
                                try:
                                    callback(tweet_data)
                                except Exception as e:
                                    print(f"❌ Callback error: {e}")

                        print("-" * 60)

                    # Check much more frequently since we're not doing heavy work
                    time.sleep(check_interval)

                except Exception as e:
                    print(f"⚠️ Error during observer check for @{username}: {e}")
                    time.sleep(check_interval)

            print(f"🛑 Stopped monitoring @{username}")

        except Exception as e:
            print(f"❌ Failed to monitor @{username}: {e}")
        finally:
            if driver:
                driver.quit()
            if username in self.monitoring_threads:
                del self.monitoring_threads[username]

    def start_monitoring(self, username: str, check_interval: int = 1) -> bool:
        """Start DOM observer monitoring"""
        if username in self.monitoring_threads:
            print(f"⚠️ Already monitoring @{username}")
            return False

        thread = threading.Thread(
            target=self.monitor_user_with_observer,
            args=(username, check_interval),
            daemon=True
        )

        self.monitoring_threads[username] = thread
        thread.start()

        print(f"✅ Started DOM Observer monitoring for @{username}")
        return True

    def stop_monitoring(self, username: str) -> bool:
        """Stop monitoring a user"""
        if username not in self.monitoring_threads:
            return False

        del self.monitoring_threads[username]
        print(f"🛑 Stopping monitoring for @{username}")
        return True

    def stop_all(self):
        """Stop all monitoring"""
        usernames = list(self.monitoring_threads.keys())
        for username in usernames:
            self.stop_monitoring(username)

    def get_monitored_users(self) -> List[str]:
        """Get monitored users"""
        return list(self.monitoring_threads.keys())

# Ultra-fast callback for real-time alerts
def instant_alert_callback(tweet_data: Dict):
    """Instant alert - fires immediately when tweet is detected"""
    print(f"\n⚡ INSTANT ALERT ⚡")
    print(f"👤 @{tweet_data['username']} just tweeted!")
    print(f"📝 {tweet_data['text']}")
    print(f"🔗 {tweet_data['url']}")
    print(f"⏰ {tweet_data['displayTime']}")
    print("=" * 50)

def discord_webhook_callback(tweet_data: Dict):
    """Send tweet to Discord webhook"""
    import requests
    from datetime import datetime

    DISCORD_WEBHOOK_URL = "https://discordapp.com/api/webhooks/1376264613137616976/ouC5Gn0k3pPDhzbR5kIct8YMfpocLsfiQuTK7_xM_b1T1O3zwT-oiP-GzLwzHen8DsjC"

    embed = {
        "title": f"🐦 @{tweet_data['username']} just tweeted!",
        "description": tweet_data['text'][:2000],
        "color": 0x1DA1F2,
        "url": tweet_data['url'],
        "timestamp": datetime.now().isoformat(),
        "fields": [
            {
                "name": "📊 Engagement",
                "value": f"💬 {tweet_data['replies']} | 🔄 {tweet_data['retweets']} | ❤️ {tweet_data['likes']}",
                "inline": True
            },
            {
                "name": "⏰ Posted",
                "value": tweet_data['displayTime'],
                "inline": True
            }
        ],
        "footer": {"text": "DOM Observer • Real-time Detection"}
    }

    payload = {
        "embeds": [embed],
        "username": "Tweet Monitor"
    }

    try:
        response = requests.post(DISCORD_WEBHOOK_URL, json=payload)
        if response.status_code == 204:
            print(f"✅ Sent to Discord: {tweet_data['text'][:50]}...")
        else:
            print(f"❌ Discord failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Discord error: {e}")

# Example usage
if __name__ == "__main__":
    print("""
⚡ DOM Observer Twitter Monitor
==============================

The most advanced monitoring approach!
Uses JavaScript MutationObserver for INSTANT detection.

✅ No polling delays
✅ Real-time DOM change detection
✅ Minimal resource usage
✅ Instant notifications

Commands:
- Type 'start <username>' to start monitoring
- Type 'stop <username>' to stop monitoring
- Type 'status' to see who's being monitored
- Type 'quit' to exit

Example: start elonmusk
    """)

    # Create monitor
    monitor = DOMObserverMonitor(headless=False)

    # Add instant callback
    monitor.add_callback(instant_alert_callback)

    # Interactive loop
    try:
        while True:
            command = input("\n> ").strip().lower()

            if command.startswith('start '):
                username = command.split(' ', 1)[1].strip().lstrip('@')
                monitor.start_monitoring(username, check_interval=0.5)  # Check every 0.5 seconds

            elif command.startswith('stop '):
                username = command.split(' ', 1)[1].strip().lstrip('@')
                monitor.stop_monitoring(username)

            elif command == 'status':
                users = monitor.get_monitored_users()
                if users:
                    print(f"📊 Currently monitoring: {', '.join(users)}")
                else:
                    print("📊 No users being monitored")

            elif command == 'quit':
                print("👋 Stopping all monitoring...")
                monitor.stop_all()
                break

            else:
                print("❓ Unknown command. Use: start <user>, stop <user>, status, quit")

    except KeyboardInterrupt:
        print("\n👋 Stopping all monitoring...")
        monitor.stop_all()
