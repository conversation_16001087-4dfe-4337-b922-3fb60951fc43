#!/usr/bin/env python3
"""
Simple X.com scraper using requests and BeautifulSoup
This is a lightweight alternative that doesn't require browser automation
"""

import requests
from bs4 import BeautifulSoup
import time
import json
from datetime import datetime
from typing import Set, List, Dict
import re

class SimpleStreamer:
    def __init__(self):
        self.seen_tweet_ids: Set[str] = set()
        self.session = requests.Session()
        
        # Headers to mimic a real browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
    
    def get_user_tweets_simple(self, username: str) -> List[Dict]:
        """
        Attempt to scrape tweets from user page using simple HTTP requests
        Note: This may not work due to X.com's JavaScript-heavy interface
        """
        try:
            print(f"🔄 Fetching page for @{username}...")
            
            url = f"https://x.com/{username}"
            response = self.session.get(url, timeout=30)
            
            if response.status_code != 200:
                print(f"❌ Failed to fetch page: HTTP {response.status_code}")
                return []
            
            # Try to extract any tweet-like content from the HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for script tags that might contain tweet data
            scripts = soup.find_all('script')
            tweets = []
            
            for script in scripts:
                if script.string and 'tweet' in script.string.lower():
                    # Try to extract JSON data
                    try:
                        # Look for JSON-like structures
                        json_matches = re.findall(r'\{[^{}]*"text"[^{}]*\}', script.string)
                        for match in json_matches:
                            try:
                                data = json.loads(match)
                                if 'text' in data:
                                    tweets.append({
                                        'text': data['text'],
                                        'username': username,
                                        'fetched_at': datetime.now().isoformat(),
                                        'source': 'simple_scraper'
                                    })
                            except:
                                continue
                    except:
                        continue
            
            print(f"  Found {len(tweets)} potential tweets")
            return tweets
            
        except Exception as e:
            print(f"❌ Error scraping @{username}: {e}")
            return []
    
    def check_nitter_instance(self, username: str, nitter_instance: str = "nitter.net") -> List[Dict]:
        """
        Try to get tweets from a Nitter instance (Twitter frontend)
        Nitter instances may be more scraping-friendly
        """
        try:
            print(f"🔄 Checking Nitter for @{username}...")
            
            url = f"https://{nitter_instance}/{username}"
            response = self.session.get(url, timeout=30)
            
            if response.status_code != 200:
                print(f"❌ Nitter request failed: HTTP {response.status_code}")
                return []
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for tweet containers in Nitter
            tweet_containers = soup.find_all('div', class_='timeline-item')
            tweets = []
            
            for container in tweet_containers:
                try:
                    # Extract tweet text
                    tweet_content = container.find('div', class_='tweet-content')
                    if not tweet_content:
                        continue
                    
                    text = tweet_content.get_text(strip=True)
                    
                    # Extract timestamp
                    time_element = container.find('span', class_='tweet-date')
                    timestamp = time_element.get('title') if time_element else None
                    
                    # Extract tweet ID from link
                    tweet_link = container.find('a', class_='tweet-link')
                    tweet_id = None
                    if tweet_link:
                        href = tweet_link.get('href', '')
                        tweet_id = href.split('/')[-1] if '/' in href else None
                    
                    if text and tweet_id and tweet_id not in self.seen_tweet_ids:
                        self.seen_tweet_ids.add(tweet_id)
                        
                        tweets.append({
                            'id': tweet_id,
                            'text': text,
                            'username': username,
                            'timestamp': timestamp,
                            'fetched_at': datetime.now().isoformat(),
                            'source': f'nitter_{nitter_instance}'
                        })
                
                except Exception as e:
                    print(f"  ⚠️ Error parsing tweet container: {e}")
                    continue
            
            print(f"  Found {len(tweets)} new tweets from Nitter")
            return tweets
            
        except Exception as e:
            print(f"❌ Error checking Nitter for @{username}: {e}")
            return []
    
    def try_rss_feed(self, username: str) -> List[Dict]:
        """
        Try to get tweets from RSS feed (if available)
        Some users may have RSS feeds enabled
        """
        try:
            print(f"🔄 Checking RSS for @{username}...")
            
            # Try common RSS feed URLs
            rss_urls = [
                f"https://nitter.net/{username}/rss",
                f"https://nitter.it/{username}/rss",
                f"https://nitter.pussthecat.org/{username}/rss"
            ]
            
            for rss_url in rss_urls:
                try:
                    response = self.session.get(rss_url, timeout=15)
                    if response.status_code == 200:
                        # Parse RSS feed
                        soup = BeautifulSoup(response.text, 'xml')
                        items = soup.find_all('item')
                        
                        tweets = []
                        for item in items:
                            title = item.find('title')
                            description = item.find('description')
                            pub_date = item.find('pubDate')
                            link = item.find('link')
                            
                            if title and description:
                                tweet_id = link.text.split('/')[-1] if link else None
                                
                                if tweet_id and tweet_id not in self.seen_tweet_ids:
                                    self.seen_tweet_ids.add(tweet_id)
                                    
                                    tweets.append({
                                        'id': tweet_id,
                                        'text': description.text,
                                        'username': username,
                                        'timestamp': pub_date.text if pub_date else None,
                                        'url': link.text if link else None,
                                        'fetched_at': datetime.now().isoformat(),
                                        'source': f'rss_{rss_url}'
                                    })
                        
                        if tweets:
                            print(f"  ✅ Found {len(tweets)} tweets from RSS")
                            return tweets
                
                except Exception as e:
                    print(f"  ⚠️ RSS URL {rss_url} failed: {e}")
                    continue
            
            print(f"  ❌ No working RSS feeds found for @{username}")
            return []
            
        except Exception as e:
            print(f"❌ Error checking RSS for @{username}: {e}")
            return []
    
    def stream_users(self, usernames: List[str], interval: int = 300, use_nitter: bool = True, use_rss: bool = True):
        """
        Stream tweets from multiple users using various methods
        """
        print(f"🚀 Starting simple streaming for users: {usernames}")
        print(f"📊 Checking every {interval} seconds")
        print(f"🔧 Methods: Nitter={use_nitter}, RSS={use_rss}")
        print("Press Ctrl+C to stop")
        
        try:
            while True:
                for username in usernames:
                    all_new_tweets = []
                    
                    # Try Nitter instances
                    if use_nitter:
                        nitter_instances = ["nitter.net", "nitter.it", "nitter.pussthecat.org"]
                        for instance in nitter_instances:
                            tweets = self.check_nitter_instance(username, instance)
                            all_new_tweets.extend(tweets)
                            if tweets:  # If we got tweets from this instance, don't try others
                                break
                            time.sleep(2)  # Small delay between instances
                    
                    # Try RSS feeds
                    if use_rss:
                        rss_tweets = self.try_rss_feed(username)
                        all_new_tweets.extend(rss_tweets)
                    
                    # Display new tweets
                    if all_new_tweets:
                        print(f"\n📱 {len(all_new_tweets)} new tweet(s) from @{username}:")
                        for tweet in all_new_tweets:
                            print(f"  🐦 {tweet['text'][:100]}...")
                            print(f"     ⏰ {tweet.get('timestamp', 'Unknown time')}")
                            print(f"     📍 Source: {tweet['source']}")
                            print()
                    else:
                        print(f"  ℹ️ No new tweets from @{username}")
                
                print(f"⏳ Waiting {interval} seconds before next check...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n👋 Stopping streamer...")

if __name__ == "__main__":
    # Example usage
    streamer = SimpleStreamer()
    
    # Users to track
    users_to_track = ["elonmusk", "OpenAI", "sama"]
    
    # Start streaming (check every 5 minutes)
    streamer.stream_users(users_to_track, interval=300, use_nitter=True, use_rss=True)
