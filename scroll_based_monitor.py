#!/usr/bin/env python3
"""
Scroll-based Tweet Monitor - Much better approach!
Uses scrolling to trigger real-time tweet loading instead of page refreshes
"""

import time
import threading
from datetime import datetime
from typing import Set, Dict, Optional, Callable, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys

class ScrollBasedMonitor:
    def __init__(self, headless: bool = True):
        self.headless = headless
        self.monitoring_threads = {}
        self.callbacks = []
        
    def add_callback(self, callback: Callable[[Dict], None]):
        """Add a callback function to handle new tweets"""
        self.callbacks.append(callback)
    
    def setup_driver(self):
        """Setup Chrome driver with optimized settings"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # Performance and stealth options
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Keep images for better tweet detection
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        return driver
    
    def extract_tweet_data(self, tweet_element, username: str) -> Optional[Dict]:
        """Extract tweet data from DOM element"""
        try:
            # Get tweet text
            try:
                text_element = tweet_element.find_element(By.XPATH, ".//div[@data-testid='tweetText']")
                tweet_text = text_element.text
            except:
                # Sometimes tweets are just media or retweets without text
                tweet_text = "[Media/Retweet]"
            
            # Get tweet URL and ID
            try:
                link_element = tweet_element.find_element(By.XPATH, ".//a[contains(@href, '/status/')]")
                tweet_url = link_element.get_attribute('href')
                tweet_id = tweet_url.split('/status/')[-1].split('?')[0] if tweet_url else None
            except:
                return None  # Can't identify tweet without URL
            
            # Get timestamp
            try:
                time_element = tweet_element.find_element(By.XPATH, ".//time")
                timestamp = time_element.get_attribute('datetime')
                display_time = time_element.text
            except:
                timestamp = None
                display_time = "Unknown"
            
            # Get engagement metrics (optional)
            try:
                reply_element = tweet_element.find_element(By.XPATH, ".//div[@data-testid='reply']//span[@data-testid='app-text-transition-container']")
                reply_count = reply_element.text if reply_element.text else "0"
            except:
                reply_count = "0"
            
            try:
                retweet_element = tweet_element.find_element(By.XPATH, ".//div[@data-testid='retweet']//span[@data-testid='app-text-transition-container']")
                retweet_count = retweet_element.text if retweet_element.text else "0"
            except:
                retweet_count = "0"
            
            try:
                like_element = tweet_element.find_element(By.XPATH, ".//div[@data-testid='like']//span[@data-testid='app-text-transition-container']")
                like_count = like_element.text if like_element.text else "0"
            except:
                like_count = "0"
            
            if tweet_id:
                return {
                    'id': tweet_id,
                    'text': tweet_text,
                    'username': username,
                    'url': tweet_url,
                    'timestamp': timestamp,
                    'display_time': display_time,
                    'replies': reply_count,
                    'retweets': retweet_count,
                    'likes': like_count,
                    'detected_at': datetime.now().isoformat()
                }
        except Exception as e:
            print(f"⚠️ Error extracting tweet: {e}")
            return None
    
    def scroll_to_top_and_wait(self, driver):
        """Scroll to top and wait for new content to load"""
        try:
            # Scroll to the very top
            driver.execute_script("window.scrollTo(0, 0);")
            
            # Small wait for content to load
            time.sleep(1)
            
            # Alternative: Use Page Up key for more natural scrolling
            # body = driver.find_element(By.TAG_NAME, "body")
            # body.send_keys(Keys.HOME)
            
        except Exception as e:
            print(f"⚠️ Error scrolling: {e}")
    
    def monitor_user_with_scroll(self, username: str, scroll_interval: int = 3):
        """Monitor a user using scroll-based detection"""
        print(f"🔄 Starting scroll-based monitoring for @{username} (scroll every {scroll_interval}s)")
        
        driver = None
        seen_tweet_ids: Set[str] = set()
        last_tweet_count = 0
        
        try:
            driver = self.setup_driver()
            
            # Navigate to user profile
            print(f"🌐 Loading https://x.com/{username}")
            driver.get(f"https://x.com/{username}")
            
            # Wait for initial tweets to load
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.XPATH, "//article[@data-testid='tweet']"))
            )
            
            print(f"✅ Loaded @{username}'s profile")
            
            # Get initial tweets to establish baseline
            initial_tweets = driver.find_elements(By.XPATH, "//article[@data-testid='tweet']")
            for tweet_element in initial_tweets:
                tweet_data = self.extract_tweet_data(tweet_element, username)
                if tweet_data and tweet_data['id']:
                    seen_tweet_ids.add(tweet_data['id'])
            
            last_tweet_count = len(seen_tweet_ids)
            print(f"📊 Baseline: {last_tweet_count} existing tweets")
            print(f"🔍 Now monitoring for new tweets... (scroll every {scroll_interval}s)")
            
            # Scroll-based monitoring loop
            while username in self.monitoring_threads:
                try:
                    # Scroll to top to trigger new tweet loading
                    self.scroll_to_top_and_wait(driver)
                    
                    # Get current tweets after scroll
                    current_tweets = driver.find_elements(By.XPATH, "//article[@data-testid='tweet']")
                    
                    new_tweets_found = []
                    for tweet_element in current_tweets:
                        tweet_data = self.extract_tweet_data(tweet_element, username)
                        
                        if tweet_data and tweet_data['id'] not in seen_tweet_ids:
                            seen_tweet_ids.add(tweet_data['id'])
                            new_tweets_found.append(tweet_data)
                    
                    # Process new tweets
                    if new_tweets_found:
                        print(f"🆕 Found {len(new_tweets_found)} new tweet(s) from @{username}!")
                        
                        for tweet_data in new_tweets_found:
                            print(f"   📝 {tweet_data['text'][:80]}...")
                            print(f"   ⏰ {tweet_data['display_time']} | 💬 {tweet_data['replies']} | 🔄 {tweet_data['retweets']} | ❤️ {tweet_data['likes']}")
                            
                            # Call all registered callbacks
                            for callback in self.callbacks:
                                try:
                                    callback(tweet_data)
                                except Exception as e:
                                    print(f"❌ Callback error: {e}")
                        
                        print("-" * 60)
                    else:
                        # Show periodic status
                        current_count = len(current_tweets)
                        if current_count != last_tweet_count:
                            print(f"ℹ️ Timeline updated: {current_count} tweets visible (was {last_tweet_count})")
                            last_tweet_count = current_count
                        else:
                            print(f"ℹ️ No new tweets from @{username} (checked {len(current_tweets)} tweets)")
                    
                    # Wait before next scroll
                    time.sleep(scroll_interval)
                    
                except Exception as e:
                    print(f"⚠️ Error during scroll check for @{username}: {e}")
                    time.sleep(scroll_interval)
            
            print(f"🛑 Stopped monitoring @{username}")
            
        except Exception as e:
            print(f"❌ Failed to monitor @{username}: {e}")
        finally:
            if driver:
                driver.quit()
            # Clean up
            if username in self.monitoring_threads:
                del self.monitoring_threads[username]
    
    def start_monitoring(self, username: str, scroll_interval: int = 3) -> bool:
        """Start monitoring a user with scroll-based detection"""
        if username in self.monitoring_threads:
            print(f"⚠️ Already monitoring @{username}")
            return False
        
        # Create and start monitoring thread
        thread = threading.Thread(
            target=self.monitor_user_with_scroll,
            args=(username, scroll_interval),
            daemon=True
        )
        
        self.monitoring_threads[username] = thread
        thread.start()
        
        print(f"✅ Started scroll-based monitoring for @{username}")
        return True
    
    def stop_monitoring(self, username: str) -> bool:
        """Stop monitoring a user"""
        if username not in self.monitoring_threads:
            print(f"⚠️ Not monitoring @{username}")
            return False
        
        del self.monitoring_threads[username]
        print(f"🛑 Stopping monitoring for @{username}")
        return True
    
    def stop_all(self):
        """Stop monitoring all users"""
        usernames = list(self.monitoring_threads.keys())
        for username in usernames:
            self.stop_monitoring(username)
    
    def get_monitored_users(self) -> List[str]:
        """Get list of currently monitored users"""
        return list(self.monitoring_threads.keys())

# Enhanced callback examples
def enhanced_discord_callback(webhook_url: str):
    """Enhanced Discord callback with rich formatting"""
    import requests
    
    def callback(tweet_data: Dict):
        embed = {
            "title": f"🐦 @{tweet_data['username']} tweeted",
            "description": tweet_data['text'][:2000],
            "color": 0x1DA1F2,
            "url": tweet_data['url'],
            "timestamp": datetime.now().isoformat(),
            "fields": [
                {
                    "name": "📊 Engagement",
                    "value": f"💬 {tweet_data['replies']} | 🔄 {tweet_data['retweets']} | ❤️ {tweet_data['likes']}",
                    "inline": True
                },
                {
                    "name": "⏰ Posted",
                    "value": tweet_data['display_time'],
                    "inline": True
                }
            ],
            "footer": {"text": "Scroll-based Monitor"}
        }
        
        payload = {
            "embeds": [embed],
            "username": "Tweet Monitor"
        }
        
        try:
            response = requests.post(webhook_url, json=payload)
            if response.status_code == 204:
                print(f"✅ Sent to Discord: {tweet_data['text'][:30]}...")
            else:
                print(f"❌ Discord failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Discord error: {e}")
    
    return callback

def real_time_alert_callback(tweet_data: Dict):
    """Real-time alert with engagement metrics"""
    print(f"\n🚨 REAL-TIME ALERT 🚨")
    print(f"👤 @{tweet_data['username']} just tweeted!")
    print(f"📝 {tweet_data['text']}")
    print(f"📊 💬 {tweet_data['replies']} | 🔄 {tweet_data['retweets']} | ❤️ {tweet_data['likes']}")
    print(f"🔗 {tweet_data['url']}")
    print(f"⏰ {tweet_data['display_time']}")
    print("=" * 60)

# Example usage
if __name__ == "__main__":
    print("""
🚀 Scroll-Based Twitter Monitor
==============================

This monitor uses scrolling instead of page refreshes!
Much faster and more real-time detection.

Commands:
- Type 'start <username>' to start monitoring
- Type 'stop <username>' to stop monitoring  
- Type 'status' to see who's being monitored
- Type 'quit' to exit

Example: start elonmusk
    """)
    
    # Create monitor
    monitor = ScrollBasedMonitor(headless=False)  # Set to True to hide browser
    
    # Add callbacks
    monitor.add_callback(real_time_alert_callback)
    
    # Uncomment to add Discord webhook
    # DISCORD_WEBHOOK = "https://discord.com/api/webhooks/YOUR_WEBHOOK_URL"
    # monitor.add_callback(enhanced_discord_callback(DISCORD_WEBHOOK))
    
    # Interactive command loop
    try:
        while True:
            command = input("\n> ").strip().lower()
            
            if command.startswith('start '):
                username = command.split(' ', 1)[1].strip().lstrip('@')
                monitor.start_monitoring(username, scroll_interval=2)  # Scroll every 2 seconds
                
            elif command.startswith('stop '):
                username = command.split(' ', 1)[1].strip().lstrip('@')
                monitor.stop_monitoring(username)
                
            elif command == 'status':
                users = monitor.get_monitored_users()
                if users:
                    print(f"📊 Currently monitoring: {', '.join(users)}")
                else:
                    print("📊 No users being monitored")
                    
            elif command == 'quit':
                print("👋 Stopping all monitoring...")
                monitor.stop_all()
                break
                
            else:
                print("❓ Unknown command. Use: start <user>, stop <user>, status, quit")
                
    except KeyboardInterrupt:
        print("\n👋 Stopping all monitoring...")
        monitor.stop_all()
