]:{silent:!1};for(const r in a)(o(a[r])||Array.isArray(a[r]))&&this.addResource(e,d,r,a[r],{silent:!0});r.silent||this.emit("added",e,d,a)}addResourceBundle(e,d,a,o,r){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},l=[e,d];e.indexOf(".")>-1&&(l=e.split("."),o=a,a=d,d=l[1]),this.addNamespaces(d);let i=t(this.data,l)||{};n.skipCopy||(a=JSON.parse(JSON.stringify(a))),o?b(i,a,r):i={...i,...a},s(this.data,l,i),n.silent||this.emit("added",e,d,a)}removeResourceBundle(e,d){this.hasResourceBundle(e,d)&&delete this.data[e][d],this.removeNamespaces(d),this.emit("removed",e,d)}hasResourceBundle(e,d){return void 0!==this.getResource(e,d)}getResourceBundle(e,d){return d||(d=this.options.defaultNS),"v1"===this.options.compatibilityAPI?{...this.getResource(e,d)}:this.getResource(e,d)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const d=this.getDataByLanguage(e);return!!(d&&Object.keys(d)||[]).find((e=>d[e]&&Object.keys(d[e]).length>0))}toJSON(){return this.data}}var _={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,d,a,o,r){return e.forEach((e=>{this.processors[e]&&(d=this.processors[e].process(d,a,o,r))})),d}};const k={};class P extends v{constructor(e){let d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),((e,d,a)=>{e.forEach((e=>{d[e]&&(a[e]=d[e])}))})(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=d,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=f.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(null==e)return!1;const a=this.resolve(e,d);return a&&void 0!==a.res}extractFromKey(e,d){let a=void 0!==d.nsSeparator?d.nsSeparator:this.options.nsSeparator;void 0===a&&(a=":");const r=void 0!==d.keySeparator?d.keySeparator:this.options.keySeparator;let n=d.ns||this.options.defaultNS||[];const l=a&&e.indexOf(a)>-1,i=!(this.options.userDefinedKeySeparator||d.keySeparator||this.options.userDefinedNsSeparator||d.nsSeparator||((e,d,a)=>{d=d||"",a=a||"";const o=S.filter((e=>d.indexOf(e)<0&&a.indexOf(e)<0));if(0===o.length)return!0;const r=m.getRegExp(`(${o.map((e=>"?"===e?"\\?":e)).join("|")})`);let n=!r.test(e);if(!n){const d=e.indexOf(a);d>0&&!r.test(e.substring(0,d))&&(n=!0)}return n})(e,a,r));if(l&&!i){const d=e.match(this.interpolator.nestingRegexp);if(d&&d.length>0)return{key:e,namespaces:o(n)?[n]:n};const l=e.split(a);(a!==r||a===r&&this.options.ns.indexOf(l[0])>-1)&&(n=l.shift()),e=l.join(r)}return{key:e,namespaces:o(n)?[n]:n}}translate(e,d,a){if("object"!=typeof d&&this.options.overloadTranslationOptionHandler&&(d=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof d&&(d={...d}),d||(d={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);const r=void 0!==d.returnDetails?d.returnDetails:this.options.returnDetails,n=void 0!==d.keySeparator?d.keySeparator:this.options.keySeparator,{key:l,namespaces:i}=this.extractFromKey(e[e.length-1],d),u=i[i.length-1],c=d.lng||this.language,s=d.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(c&&"cimode"===c.toLowerCase()){if(s){const e=d.nsSeparator||this.options.nsSeparator;return r?{res:`${u}${e}${l}`,usedKey:l,exactUsedKey:l,usedLng:c,usedNS:u,usedParams:this.getUsedParamsDetails(d)}:`${u}${e}${l}`}return r?{res:l,usedKey:l,exactUsedKey:l,usedLng:c,usedNS:u,usedParams:this.getUsedParamsDetails(d)}:l}const t=this.resolve(e,d);let b=t&&t.res;const D=t&&t.usedKey||l,h=t&&t.exactUsedKey||l,p=Object.prototype.toString.apply(b),S=void 0!==d.joinArrays?d.joinArrays:this.options.joinArrays,m=!this.i18nFormat||this.i18nFormat.handleAsObject,A=!o(b)&&"boolean"!=typeof b&&"number"!=typeof b;if(!(m&&b&&A&&["[object Number]","[object Function]","[object RegExp]"].indexOf(p)<0)||o(S)&&Array.isArray(b))if(m&&o(S)&&Array.isArray(b))b=b.join(S),b&&(b=this.extendTranslation(b,e,d,a));else{let r=!1,i=!1;const s=void 0!==d.count&&!o(d.count),D=P.hasDefaultValue(d),h=s?this.pluralResolver.getSuffix(c,d.count,d):"",p=d.ordinal&&s?this.pluralResolver.getSuffix(c,d.count,{ordinal:!1}):"",S=s&&!d.ordinal&&0===d.count&&this.pluralResolver.shouldUseIntlApi(),m=S&&d[`defaultValue${this.options.pluralSeparator}zero`]||d[`defaultValue${h}`]||d[`defaultValue${p}`]||d.defaultValue;!this.isValidLookup(b)&&D&&(r=!0,b=m),this.isValidLookup(b)||(i=!0,b=l);const A=(d.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&i?void 0:b,M=D&&m!==b&&this.options.updateMissing;if(i||r||M){if(this.logger.log(M?"updateKey":"missingKey",c,u,l,M?m:b),n){const e=this.resolve(l,{...d,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[];const a=this.languageUtils.getFallbackCodes(this.options.fallbackLng,d.lng||this.language);if("fallback"===this.options.saveMissingTo&&a&&a[0])for(let d=0;d<a.length;d++)e.push(a[d]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(d.lng||this.language):e.push(d.lng||this.language);const o=(e,a,o)=>{const r=D&&o!==b?o:A;this.options.missingKeyHandler?this.options.missingKeyHandler(e,u,a,r,M,d):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(e,u,a,r,M,d),this.emit("missingKey",e,u,a,b)};this.options.saveMissing&&(this.options.saveMissingPlurals&&s?e.forEach((e=>{const a=this.pluralResolver.getSuffixes(e,d);S&&d[`defaultValue${this.options.pluralSeparator}zero`]&&a.indexOf(`${this.options.pluralSeparator}zero`)<0&&a.push(`${this.options.pluralSeparator}zero`),a.forEach((a=>{o([e],l+a,d[`defaultValue${a}`]||m)}))})):o(e,l,m))}b=this.extendTranslation(b,e,d,t,a),i&&b===l&&this.options.appendNamespaceToMissingKey&&(b=`${u}:${l}`),(i||r)&&this.options.parseMissingKeyHandler&&(b="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${u}:${l}`:l,r?b:void 0):this.options.parseMissingKeyHandler(b))}else{if(!d.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(D,b,{...d,ns:i}):`key '${l} (${this.language})' returned an object instead of string.`;return r?(t.res=e,t.usedParams=this.getUsedParamsDetails(d),t):e}if(n){const e=Array.isArray(b),a=e?[]:{},o=e?h:D;for(const e in b)if(Object.prototype.hasOwnProperty.call(b,e)){const r=`${o}${n}${e}`;a[e]=this.translate(r,{...d,joinArrays:!1,ns:i}),a[e]===r&&(a[e]=b[e])}b=a}}return r?(t.res=b,t.usedParams=this.getUsedParamsDetails(d),t):b}extendTranslation(e,d,a,r,n){var l=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...a},a.lng||this.language||r.usedLng,r.usedNS,r.usedKey,{resolved:r});else if(!a.skipInterpolation){a.interpolation&&this.interpolator.init({...a,interpolation:{...this.options.interpolation,...a.interpolation}});const i=o(e)&&(a&&a.interpolation&&void 0!==a.interpolation.skipOnVariables?a.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let u;if(i){const d=e.match(this.interpolator.nestingRegexp);u=d&&d.length}let c=a.replace&&!o(a.replace)?a.replace:a;if(this.options.interpolation.defaultVariables&&(c={...this.options.interpolation.defaultVariables,...c}),e=this.interpolator.interpolate(e,c,a.lng||this.language||r.usedLng,a),i){const d=e.match(this.interpolator.nestingRegexp);u<(d&&d.length)&&(a.nest=!1)}!a.lng&&"v1"!==this.options.compatibilityAPI&&r&&r.res&&(a.lng=this.language||r.usedLng),!1!==a.nest&&(e=this.interpolator.nest(e,(function(){for(var e=arguments.length,o=new Array(e),r=0;r<e;r++)o[r]=arguments[r];return n&&n[0]===o[0]&&!a.context?(l.logger.warn(`It seems you are nesting recursively key: ${o[0]} in key: ${d[0]}`),null):l.translate(...o,d)}),a)),a.interpolation&&this.interpolator.reset()}const i=a.postProcess||this.options.postProcess,u=o(i)?[i]:i;return null!=e&&u&&u.length&&!1!==a.applyPostProcessor&&(e=_.handle(u,e,d,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...r,usedParams:this.getUsedParamsDetails(a)},...a}:a,this)),e}resolve(e){let d,a,r,n,l,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return o(e)&&(e=[e]),e.forEach((e=>{if(this.isValidLookup(d))return;const u=this.extractFromKey(e,i),c=u.key;a=c;let s=u.namespaces;this.options.fallbackNS&&(s=s.concat(this.options.fallbackNS));const t=void 0!==i.count&&!o(i.count),b=t&&!i.ordinal&&0===i.count&&this.pluralResolver.shouldUseIntlApi(),D=void 0!==i.context&&(o(i.context)||"number"==typeof i.context)&&""!==i.context,h=i.lngs?i.lngs:this.languageUtils.toResolveHierarchy(i.lng||this.language,i.fallbackLng);s.forEach((e=>{this.isValidLookup(d)||(l=e,!k[`${h[0]}-${e}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(l)&&(k[`${h[0]}-${e}`]=!0,this.logger.warn(`key "${a}" for languages "${h.join(", ")}" won't get resolved as namespace "${l}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),h.forEach((a=>{if(this.isValidLookup(d))return;n=a;const o=[c];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(o,c,a,e,i);else{let e;t&&(e=this.pluralResolver.getSuffix(a,i.count,i));const d=`${this.options.pluralSeparator}zero`,r=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(t&&(o.push(c+e),i.ordinal&&0===e.indexOf(r)&&o.push(c+e.replace(r,this.options.pluralSeparator)),b&&o.push(c+d)),D){const a=`${c}${this.options.contextSeparator}${i.context}`;o.push(a),t&&(o.push(a+e),i.ordinal&&0===e.indexOf(r)&&o.push(a+e.replace(r,this.options.pluralSeparator)),b&&o.push(a+d))}}let l;for(;l=o.pop();)this.isValidLookup(d)||(r=l,d=this.getResource(a,e,l,i))})))}))})),{res:d,usedKey:a,exactUsedKey:r,usedLng:n,usedNS:l}}isValidLookup(e){return!(void 0===e||!this.options.returnNull&&null===e||!this.options.returnEmptyString&&""===e)}getResource(e,d,a){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,d,a,o):this.resourceStore.getResource(e,d,a,o)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const d=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],a=e.replace&&!o(e.replace);let r=a?e.replace:e;if(a&&void 0!==e.count&&(r.count=e.count),this.options.interpolation.defaultVariables&&(r={...this.options.interpolation.defaultVariables,...r}),!a){r={...r};for(const e of d)delete r[e]}return r}static hasDefaultValue(e){const d="defaultValue";for(const a in e)if(Object.prototype.hasOwnProperty.call(e,a)&&d===a.substring(0,12)&&void 0!==e[a])return!0;return!1}}const I=e=>e.charAt(0).toUpperCase()+e.slice(1);class B{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=f.create("languageUtils")}getScriptPartFromCode(e){if(!(e=M(e))||e.indexOf("-")<0)return null;const d=e.split("-");return 2===d.length?null:(d.pop(),"x"===d[d.length-1].toLowerCase()?null:this.formatLanguageCode(d.join("-")))}getLanguagePartFromCode(e){if(!(e=M(e))||e.indexOf("-")<0)return e;const d=e.split("-");return this.formatLanguageCode(d[0])}formatLanguageCode(e){if(o(e)&&e.indexOf("-")>-1){if("undefined"!=typeof Intl&&void 0!==Intl.getCanonicalLocales)try{let d=Intl.getCanonicalLocales(e)[0];if(d&&this.options.lowerCaseLng&&(d=d.toLowerCase()),d)return d}catch(e){}const d=["hans","hant","latn","cyrl","cans","mong","arab"];let a=e.split("-");return this.options.lowerCaseLng?a=a.map((e=>e.toLowerCase())):2===a.length?(a[0]=a[0].toLowerCase(),a[1]=a[1].toUpperCase(),d.indexOf(a[1].toLowerCase())>-1&&(a[1]=I(a[1].toLowerCase()))):3===a.length&&(a[0]=a[0].toLowerCase(),2===a[1].length&&(a[1]=a[1].toUpperCase()),"sgn"!==a[0]&&2===a[2].length&&(a[2]=a[2].toUpperCase()),d.indexOf(a[1].toLowerCase())>-1&&(a[1]=I(a[1].toLowerCase())),d.indexOf(a[2].toLowerCase())>-1&&(a[2]=I(a[2].toLowerCase()))),a.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let d;return e.forEach((e=>{if(d)return;const a=this.formatLanguageCode(e);this.options.supportedLngs&&!this.isSupportedCode(a)||(d=a)})),!d&&this.options.supportedLngs&&e.forEach((e=>{if(d)return;const a=this.getLanguagePartFromCode(e);if(this.isSupportedCode(a))return d=a;d=this.options.supportedLngs.find((e=>e===a?e:e.indexOf("-")<0&&a.indexOf("-")<0?void 0:e.indexOf("-")>0&&a.indexOf("-")<0&&e.substring(0,e.indexOf("-"))===a||0===e.indexOf(a)&&a.length>1?e:void 0))})),d||(d=this.getFallbackCodes(this.options.fallbackLng)[0]),d}getFallbackCodes(e,d){if(!e)return[];if("function"==typeof e&&(e=e(d)),o(e)&&(e=[e]),Array.isArray(e))return e;if(!d)return e.default||[];let a=e[d];return a||(a=e[this.getScriptPartFromCode(d)]),a||(a=e[this.formatLanguageCode(d)]),a||(a=e[this.getLanguagePartFromCode(d)]),a||(a=e.default),a||[]}toResolveHierarchy(e,d){const a=this.getFallbackCodes(d||this.options.fallbackLng||[],e),r=[],n=e=>{e&&(this.isSupportedCode(e)?r.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return o(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&n(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&n(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&n(this.getLanguagePartFromCode(e))):o(e)&&n(this.formatLanguageCode(e)),a.forEach((e=>{r.indexOf(e)<0&&n(this.formatLanguageCode(e))})),r}}let C=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],T={1:e=>Number(e>1),2:e=>Number(1!=e),3:e=>0,4:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),5:e=>Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5),6:e=>Number(1==e?0:e>=2&&e<=4?1:2),7:e=>Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),8:e=>Number(1==e?0:2==e?1:8!=e&&11!=e?2:3),9:e=>Number(e>=2),10:e=>Number(1==e?0:2==e?1:e<7?2:e<11?3:4),11:e=>Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3),12:e=>Number(e%10!=1||e%100==11),13:e=>Number(0!==e),14:e=>Number(1==e?0:2==e?1:3==e?2:3),15:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2),16:e=>Number(e%10==1&&e%100!=11?0:0!==e?1:2),17:e=>Number(1==e||e%10==1&&e%100!=11?0:1),18:e=>Number(0==e?0:1==e?1:2),19:e=>Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3),20:e=>Number(1==e?0:0==e||e%100>0&&e%100<20?1:2),21:e=>Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0),22:e=>Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)};const R=["v1","v2","v3"],E=["v4"],N={zero:0,one:1,two:2,few:3,many:4,other:5};class x{constructor(e){let d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=d,this.logger=f.create("pluralResolver"),this.options.compatibilityJSON&&!E.includes(this.options.compatibilityJSON)||"undefined"!=typeof Intl&&Intl.PluralRules||(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=(()=>{const e={};return C.forEach((d=>{d.lngs.forEach((a=>{e[a]={numbers:d.nr,plurals:T[d.fc]}}))})),e})(),this.pluralRulesCache={}}addRule(e,d){this.rules[e]=d}clearCache(){this.pluralRulesCache={}}getRule(e){let d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi()){const a=M("dev"===e?"en":e),o=d.ordinal?"ordinal":"cardinal",r=JSON.stringify({cleanedCode:a,type:o});if(r in this.pluralRulesCache)return this.pluralRulesCache[r];let n;try{n=new Intl.PluralRules(a,{type:o})}catch(a){if(!e.match(/-|_/))return;const o=this.languageUtils.getLanguagePartFromCode(e);n=this.getRule(o,d)}return this.pluralRulesCache[r]=n,n}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const a=this.getRule(e,d);return this.shouldUseIntlApi()?a&&a.resolvedOptions().pluralCategories.length>1:a&&a.numbers.length>1}getPluralFormsOfKey(e,d){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,a).map((e=>`${d}${e}`))}getSuffixes(e){let d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const a=this.getRule(e,d);return a?this.shouldUseIntlApi()?a.resolvedOptions().pluralCategories.sort(((e,d)=>N[e]-N[d])).map((e=>`${this.options.prepend}${d.ordinal?`ordinal${this.options.prepend}`:""}${e}`)):a.numbers.map((a=>this.getSuffix(e,a,d))):[]}getSuffix(e,d){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const o=this.getRule(e,a);return o?this.shouldUseIntlApi()?`${this.options.prepend}${a.ordinal?`ordinal${this.options.prepend}`:""}${o.select(d)}`:this.getSuffixRetroCompatible(o,d):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,d){const a=e.noAbs?e.plurals(d):e.plurals(Math.abs(d));let o=e.numbers[a];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===o?o="plural":1===o&&(o=""));const r=()=>this.options.prepend&&o.toString()?this.options.prepend+o.toString():o.toString();return"v1"===this.options.compatibilityJSON?1===o?"":"number"==typeof o?`_plural_${o.toString()}`:r():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?r():this.options.prepend&&a.toString()?this.options.prepend+a.toString():a.toString()}shouldUseIntlApi(){return!R.includes(this.options.compatibilityJSON)}}const Z=function(e,d,a){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",n=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],l=((e,d,a)=>{const o=t(e,a);return void 0!==o?o:t(d,a)})(e,d,a);return!l&&n&&o(a)&&(l=A(e,a,r),void 0===l&&(l=A(d,a,r))),l},O=e=>e.replace(/\$/g,"$$$$");class L{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=f.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:d,escapeValue:a,useRawValueToEscape:o,prefix:r,prefixEscaped:n,suffix:l,suffixEscaped:i,formatSeparator:u,unescapeSuffix:c,unescapePrefix:s,nestingPrefix:t,nestingPrefixEscaped:b,nestingSuffix:h,nestingSuffixEscaped:S,nestingOptionsSeparator:m,maxReplaces:A,alwaysFormat:M}=e.interpolation;this.escape=void 0!==d?d:p,this.escapeValue=void 0===a||a,this.useRawValueToEscape=void 0!==o&&o,this.prefix=r?D(r):n||"{{",this.suffix=l?D(l):i||"}}",this.formatSeparator=u||",",this.unescapePrefix=c?"":s||"-",this.unescapeSuffix=this.unescapePrefix?"":c||"",this.nestingPrefix=t?D(t):b||D("$t("),this.nestingSuffix=h?D(h):S||D(")"),this.nestingOptionsSeparator=m||",",this.maxReplaces=A||1e3,this.alwaysFormat=void 0!==M&&M,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(e,d)=>e&&e.source===d?(e.lastIndex=0,e):new RegExp(d,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,d,a,r){let l,i,u;const c=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},s=e=>{if(e.indexOf(this.formatSeparator)<0){const o=Z(d,c,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(o,void 0,a,{...r,...d,interpolationkey:e}):o}const o=e.split(this.formatSeparator),n=o.shift().trim(),l=o.join(this.formatSeparator).trim();return this.format(Z(d,c,n,this.options.keySeparator,this.options.ignoreJSONStructure),l,a,{...r,...d,interpolationkey:n})};this.resetRegExp();const t=r&&r.missingInterpolationHandler||this.options.missingInterpolationHandler,b=r&&r.interpolation&&void 0!==r.interpolation.skipOnVariables?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>O(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?O(this.escape(e)):O(e)}].forEach((d=>{for(u=0;l=d.regex.exec(e);){const a=l[1].trim();if(i=s(a),void 0===i)if("function"==typeof t){const d=t(e,l,r);i=o(d)?d:""}else if(r&&Object.prototype.hasOwnProperty.call(r,a))i="";else{if(b){i=l[0];continue}this.logger.warn(`missed to pass in variable ${a} for interpolating ${e}`),i=""}else o(i)||this.useRawValueToEscape||(i=n(i));const c=d.safeValue(i);if(e=e.replace(l[0],c),b?(d.regex.lastIndex+=i.length,d.regex.lastIndex-=l[0].length):d.regex.lastIndex=0,u++,u>=this.maxReplaces)break}})),e}nest(e,d){let a,r,l,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const u=(e,d)=>{const a=this.nestingOptionsSeparator;if(e.indexOf(a)<0)return e;const o=e.split(new RegExp(`${a}[ ]*{`));let r=`{${o[1]}`;e=o[0],r=this.interpolate(r,l);const n=r.match(/'/g),i=r.match(/"/g);(n&&n.length%2==0&&!i||i.length%2!=0)&&(r=r.replace(/'/g,'"'));try{l=JSON.parse(r),d&&(l={...d,...l})}catch(d){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,d),`${e}${a}${r}`}return l.defaultValue&&l.defaultValue.indexOf(this.prefix)>-1&&delete l.defaultValue,e};for(;a=this.nestingRegexp.exec(e);){let c=[];l={...i},l=l.replace&&!o(l.replace)?l.replace:l,l.applyPostProcessor=!1,delete l.defaultValue;let s=!1;if(-1!==a[0].indexOf(this.formatSeparator)&&!/{.*}/.test(a[1])){const e=a[1].split(this.formatSeparator).map((e=>e.trim()));a[1]=e.shift(),c=e,s=!0}if(r=d(u.call(this,a[1].trim(),l),l),r&&a[0]===e&&!o(r))return r;o(r)||(r=n(r)),r||(this.logger.warn(`missed to resolve ${a[1]} for nesting ${e}`),r=""),s&&(r=c.reduce(((e,d)=>this.format(e,d,i.lng,{...i,interpolationkey:a[1].trim()})),r.trim())),e=e.replace(a[0],r),this.regexp.lastIndex=0}return e}}const F=e=>{const d={};return(a,o,r)=>{let n=r;r&&r.interpolationkey&&r.formatParams&&r.formatParams[r.interpolationkey]&&r[r.interpolationkey]&&(n={...n,[r.interpolationkey]:void 0});const l=o+JSON.stringify(n);let i=d[l];return i||(i=e(M(o),r),d[l]=i),i(a)}};class V{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=f.create("formatter"),this.options=e,this.formats={number:F(((e,d)=>{const a=new Intl.NumberFormat(e,{...d});return e=>a.format(e)})),currency:F(((e,d)=>{const a=new Intl.NumberFormat(e,{...d,style:"currency"});return e=>a.format(e)})),datetime:F(((e,d)=>{const a=new Intl.DateTimeFormat(e,{...d});return e=>a.format(e)})),relativetime:F(((e,d)=>{const a=new Intl.RelativeTimeFormat(e,{...d});return e=>a.format(e,d.range||"day")})),list:F(((e,d)=>{const a=new Intl.ListFormat(e,{...d});return e=>a.format(e)}))},this.init(e)}init(e){let d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=d.interpolation.formatSeparator||","}add(e,d){this.formats[e.toLowerCase().trim()]=d}addCached(e,d){this.formats[e.toLowerCase().trim()]=F(d)}format(e,d,a){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const r=d.split(this.formatSeparator);if(r.length>1&&r[0].indexOf("(")>1&&r[0].indexOf(")")<0&&r.find((e=>e.indexOf(")")>-1))){const e=r.findIndex((e=>e.indexOf(")")>-1));r[0]=[r[0],...r.splice(1,e)].join(this.formatSeparator)}return r.reduce(((e,d)=>{const{formatName:r,formatOptions:n}=(e=>{let d=e.toLowerCase().trim();const a={};if(e.indexOf("(")>-1){const o=e.split("(");d=o[0].toLowerCase().trim();const r=o[1].substring(0,o[1].length-1);"currency"===d&&r.indexOf(":")<0?a.currency||(a.currency=r.trim()):"relativetime"===d&&r.indexOf(":")<0?a.range||(a.range=r.trim()):r.split(";").forEach((e=>{if(e){const[d,...o]=e.split(":"),r=o.join(":").trim().replace(/^'+|'+$/g,""),n=d.trim();a[n]||(a[n]=r),"false"===r&&(a[n]=!1),"true"===r&&(a[n]=!0),isNaN(r)||(a[n]=parseInt(r,10))}}))}return{formatName:d,formatOptions:a}})(d);if(this.formats[r]){let d=e;try{const l=o&&o.formatParams&&o.formatParams[o.interpolationkey]||{},i=l.locale||l.lng||o.locale||o.lng||a;d=this.formats[r](e,i,{...n,...o,...l})}catch(e){this.logger.warn(e)}return d}return this.logger.warn(`there was no format function for ${r}`),e}),e)}}class U extends v{constructor(e,d,a){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=d,this.services=a,this.languageUtils=a.languageUtils,this.options=o,this.logger=f.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=o.maxParallelReads||10,this.readingCalls=0,this.maxRetries=o.maxRetries>=0?o.maxRetries:5,this.retryTimeout=o.retryTimeout>=1?o.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(a,o.backend,o)}queueLoad(e,d,a,o){const r={},n={},l={},i={};return e.forEach((e=>{let o=!0;d.forEach((d=>{const l=`${e}|${d}`;!a.reload&&this.store.hasResourceBundle(e,d)?this.state[l]=2:this.state[l]<0||(1===this.state[l]?void 0===n[l]&&(n[l]=!0):(this.state[l]=1,o=!1,void 0===n[l]&&(n[l]=!0),void 0===r[l]&&(r[l]=!0),void 0===i[d]&&(i[d]=!0)))})),o||(l[e]=!0)})),(Object.keys(r).length||Object.keys(n).length)&&this.queue.push({pending:n,pendingCount:Object.keys(n).length,loaded:{},errors:[],callback:o}),{toLoad:Object.keys(r),pending:Object.keys(n),toLoadLanguages:Object.keys(l),toLoadNamespaces:Object.keys(i)}}loaded(e,d,a){const o=e.split("|"),r=o[0],n=o[1];d&&this.emit("failedLoading",r,n,d),!d&&a&&this.store.addResourceBundle(r,n,a,void 0,void 0,{skipCopy:!0}),this.state[e]=d?-1:2,d&&a&&(this.state[e]=0);const l={};this.queue.forEach((a=>{((e,d,a)=>{const{obj:o,k:r}=c(e,d,Object);o[r]=o[r]||[],o[r].push(a)})(a.loaded,[r],n),((e,d)=>{void 0!==e.pending[d]&&(delete e.pending[d],e.pendingCount--)})(a,e),d&&a.errors.push(d),0!==a.pendingCount||a.done||(Object.keys(a.loaded).forEach((e=>{l[e]||(l[e]={});const d=a.loaded[e];d.length&&d.forEach((d=>{void 0===l[e][d]&&(l[e][d]=!0)}))})),a.done=!0,a.errors.length?a.callback(a.errors):a.callback())})),this.emit("loaded",l),this.queue=this.queue.filter((e=>!e.done))}read(e,d,a){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,n=arguments.length>5?arguments[5]:void 0;if(!e.length)return n(null,{});if(this.readingCalls>=this.maxParallelReads)return void this.waitingReads.push({lng:e,ns:d,fcName:a,tried:o,wait:r,callback:n});this.readingCalls++;const l=(l,i)=>{if(this.readingCalls--,this.waitingReads.length>0){const e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}l&&i&&o<this.maxRetries?setTimeout((()=>{this.read.call(this,e,d,a,o+1,2*r,n)}),r):n(l,i)},i=this.backend[a].bind(this.backend);if(2!==i.length)return i(e,d,l);try{const a=i(e,d);a&&"function"==typeof a.then?a.then((e=>l(null,e))).catch(l):l(null,a)}catch(e){l(e)}}prepareLoading(e,d){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),r&&r();o(e)&&(e=this.languageUtils.toResolveHierarchy(e)),o(d)&&(d=[d]);const n=this.queueLoad(e,d,a,r);if(!n.toLoad.length)return n.pending.length||r(),null;n.toLoad.forEach((e=>{this.loadOne(e)}))}load(e,d,a){this.prepareLoading(e,d,{},a)}reload(e,d,a){this.prepareLoading(e,d,{reload:!0},a)}loadOne(e){let d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";const a=e.split("|"),o=a[0],r=a[1];this.read(o,r,"read",void 0,void 0,((a,n)=>{a&&this.logger.warn(`${d}loading namespace ${r} for language ${o} failed`,a),!a&&n&&this.logger.log(`${d}loaded namespace ${r} for language ${o}`,n),this.loaded(e,a,n)}))}saveMissing(e,d,a,o,r){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},l=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(d))this.logger.warn(`did not save key "${a}" as the namespace "${d}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");else if(null!=a&&""!==a){if(this.backend&&this.backend.create){const i={...n,isUpdate:r},u=this.backend.create.bind(this.backend);if(u.length<6)try{let r;r=5===u.length?u(e,d,a,o,i):u(e,d,a,o),r&&"function"==typeof r.then?r.then((e=>l(null,e))).catch(l):l(null,r)}catch(e){l(e)}else u(e,d,a,o,l,i)}e&&e[0]&&this.store.addResource(e[0],d,a,o)}}}const H=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let d={};if("object"==typeof e[1]&&(d=e[1]),o(e[1])&&(d.defaultValue=e[1]),o(e[2])&&(d.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){const a=e[3]||e[2];Object.keys(a).forEach((e=>{d[e]=a[e]}))}return d},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),j=e=>(o(e.ns)&&(e.ns=[e.ns]),o(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),o(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&e.supportedLngs.indexOf("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e),z=()=>{};class G extends v{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},d=arguments.length>1?arguments[1]:void 0;var a;if(super(),this.options=j(e),this.services={},this.logger=f,this.modules={external:[]},a=this,Object.getOwnPropertyNames(Object.getPrototypeOf(a)).forEach((e=>{"function"==typeof a[e]&&(a[e]=a[e].bind(a))})),d&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,d),this;setTimeout((()=>{this.init(e,d)}),0)}}init(){var e=this;let d=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"==typeof d&&(a=d,d={}),!d.defaultNS&&!1!==d.defaultNS&&d.ns&&(o(d.ns)?d.defaultNS=d.ns:d.ns.indexOf("translation")<0&&(d.defaultNS=d.ns[0]));const n=H();this.options={...n,...this.options,...j(d)},"v1"!==this.options.compatibilityAPI&&(this.options.interpolation={...n.interpolation,...this.options.interpolation}),void 0!==d.keySeparator&&(this.options.userDefinedKeySeparator=d.keySeparator),void 0!==d.nsSeparator&&(this.options.userDefinedNsSeparator=d.nsSeparator);const l=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let d;this.modules.logger?f.init(l(this.modules.logger),this.options):f.init(null,this.options),this.modules.formatter?d=this.modules.formatter:"undefined"!=typeof Intl&&(d=V);const a=new B(this.options);this.store=new g(this.options.resources,this.options);const o=this.services;o.logger=f,o.resourceStore=this.store,o.languageUtils=a,o.pluralResolver=new x(a,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),!d||this.options.interpolation.format&&this.options.interpolation.format!==n.interpolation.format||(o.formatter=l(d),o.formatter.init(o,this.options),this.options.interpolation.format=o.formatter.format.bind(o.formatter)),o.interpolator=new L(this.options),o.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},o.backendConnector=new U(l(this.modules.backend),o.resourceStore,o,this.options),o.backendConnector.on("*",(function(d){for(var a=arguments.length,o=new Array(a>1?a-1:0),r=1;r<a;r++)o[r-1]=arguments[r];e.emit(d,...o)})),this.modules.languageDetector&&(o.languageDetector=l(this.modules.languageDetector),o.languageDetector.init&&o.languageDetector.init(o,this.options.detection,this.options)),this.modules.i18nFormat&&(o.i18nFormat=l(this.modules.i18nFormat),o.i18nFormat.init&&o.i18nFormat.init(this)),this.translator=new P(this.services,this.options),this.translator.on("*",(function(d){for(var a=arguments.length,o=new Array(a>1?a-1:0),r=1;r<a;r++)o[r-1]=arguments[r];e.emit(d,...o)})),this.modules.external.forEach((e=>{e.init&&e.init(this)}))}if(this.format=this.options.interpolation.format,a||(a=z),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach((d=>{this[d]=function(){return e.store[d](...arguments)}}));["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach((d=>{this[d]=function(){return e.store[d](...arguments),e}}));const i=r(),u=()=>{const e=(e,d)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),i.resolve(d),a(e,d)};if(this.languages&&"v1"!==this.options.compatibilityAPI&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initImmediate?u():setTimeout(u,0),i}loadResources(e){let d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:z;const a=o(e)?e:this.language;if("function"==typeof e&&(d=e),!this.options.resources||this.options.partialBundledLanguages){if(a&&"cimode"===a.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return d();const e=[],o=d=>{if(!d)return;if("cimode"===d)return;this.services.languageUtils.toResolveHierarchy(d).forEach((d=>{"cimode"!==d&&e.indexOf(d)<0&&e.push(d)}))};if(a)o(a);else{this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach((e=>o(e)))}this.options.preload&&this.options.preload.forEach((e=>o(e))),this.services.backendConnector.load(e,this.options.ns,(e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),d(e)}))}else d(null)}reloadResources(e,d,a){const o=r();return"function"==typeof e&&(a=e,e=void 0),"function"==typeof d&&(a=d,d=void 0),e||(e=this.languages),d||(d=this.options.ns),a||(a=z),this.services.backendConnector.reload(e,d,(e=>{o.resolve(),a(e)})),o}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&_.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(let e=0;e<this.languages.length;e++){const d=this.languages[e];if(!(["cimode","dev"].indexOf(d)>-1)&&this.store.hasLanguageSomeTranslations(d)){this.resolvedLanguage=d;break}}}changeLanguage(e,d){var a=this;this.isLanguageChangingTo=e;const n=r();this.emit("languageChanging",e);const l=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},i=(e,o)=>{o?(l(o),this.translator.changeLanguage(o),this.isLanguageChangingTo=void 0,this.emit("languageChanged",o),this.logger.log("languageChanged",o)):this.isLanguageChangingTo=void 0,n.resolve((function(){return a.t(...arguments)})),d&&d(e,(function(){return a.t(...arguments)}))},u=d=>{e||d||!this.services.languageDetector||(d=[]);const a=o(d)?d:this.services.languageUtils.getBestMatchFromCodes(d);a&&(this.language||l(a),this.translator.language||this.translator.changeLanguage(a),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(a)),this.loadResources(a,(e=>{i(e,a)}))};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(u):this.services.languageDetector.detect(u):u(e):u(this.services.languageDetector.detect()),n}getFixedT(e,d,a){var r=this;const n=function(e,d){let o;if("object"!=typeof d){for(var l=arguments.length,i=new Array(l>2?l-2:0),u=2;u<l;u++)i[u-2]=arguments[u];o=r.options.overloadTranslationOptionHandler([e,d].concat(i))}else o={...d};o.lng=o.lng||n.lng,o.lngs=o.lngs||n.lngs,o.ns=o.ns||n.ns,""!==o.keyPrefix&&(o.keyPrefix=o.keyPrefix||a||n.keyPrefix);const c=r.options.keySeparator||".";let s;return s=o.keyPrefix&&Array.isArray(e)?e.map((e=>`${o.keyPrefix}${c}${e}`)):o.keyPrefix?`${o.keyPrefix}${c}${e}`:e,r.t(s,o)};return o(e)?n.lng=e:n.lngs=e,n.ns=d,n.keyPrefix=a,n}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const a=d.lng||this.resolvedLanguage||this.languages[0],o=!!this.options&&this.options.fallbackLng,r=this.languages[this.languages.length-1];if("cimode"===a.toLowerCase())return!0;const n=(e,d)=>{const a=this.services.backendConnector.state[`${e}|${d}`];return-1===a||0===a||2===a};if(d.precheck){const e=d.precheck(this,n);if(void 0!==e)return e}return!!this.hasResourceBundle(a,e)||(!(this.services.backendConnector.backend&&(!this.options.resources||this.options.partialBundledLanguages))||!(!n(a,e)||o&&!n(r,e)))}loadNamespaces(e,d){const a=r();return this.options.ns?(o(e)&&(e=[e]),e.forEach((e=>{this.options.ns.indexOf(e)<0&&this.options.ns.push(e)})),this.loadResources((e=>{a.resolve(),d&&d(e)})),a):(d&&d(),Promise.resolve())}loadLanguages(e,d){const a=r();o(e)&&(e=[e]);const n=this.options.preload||[],l=e.filter((e=>n.indexOf(e)<0&&this.services.languageUtils.isSupportedCode(e)));return l.length?(this.options.preload=n.concat(l),this.loadResources((e=>{a.resolve(),d&&d(e)})),a):(d&&d(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";const d=this.services&&this.services.languageUtils||new B(H());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(d.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){return new G(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},arguments.length>1?arguments[1]:void 0)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:z;const a=e.forkResourceStore;a&&delete e.forkResourceStore;const o={...this.options,...e,isClone:!0},r=new G(o);void 0===e.debug&&void 0===e.prefix||(r.logger=r.logger.clone(e));return["store","services","language"].forEach((e=>{r[e]=this[e]})),r.services={...this.services},r.services.utils={hasLoadedNamespace:r.hasLoadedNamespace.bind(r)},a&&(r.store=new g(this.store.data,o),r.services.resourceStore=r.store),r.translator=new P(r.services,o),r.translator.on("*",(function(e){for(var d=arguments.length,a=new Array(d>1?d-1:0),o=1;o<d;o++)a[o-1]=arguments[o];r.emit(e,...a)})),r.init(o,d),r.translator.options=o,r.translator.backendConnector.services.utils={hasLoadedNamespace:r.hasLoadedNamespace.bind(r)},r}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const q=G.createInstance();q.createInstance=G.createInstance;const W=q.createInstance;q.dir,q.init,q.loadResources,q.reloadResources,q.use,q.changeLanguage,q.getFixedT,q.t,q.exists,q.setDefaultNamespace,q.hasLoadedNamespace,q.loadNamespaces,q.loadLanguages},400752:(e,d,a)=>{"use strict";a.d(d,{Dv:()=>s,KO:()=>b,b9:()=>t,oR:()=>l,zt:()=>i});var o=a(202784),r=a(565058);const n=(0,o.createContext)(void 0),l=e=>{const d=(0,o.useContext)(n);return(null==e?void 0:e.store)||d||(0,r.K7)()},i=({children:e,store:d})=>{const a=(0,o.useRef)();return d||a.current||(a.current=(0,r.MT)()),(0,o.createElement)(n.Provider,{value:d||a.current},e)},u=e=>"function"==typeof(null==e?void 0:e.then),c=o.use||(e=>{if("pending"===e.status)throw e;if("fulfilled"===e.status)return e.value;throw"rejected"===e.status?e.reason:(e.status="pending",e.then((d=>{e.status="fulfilled",e.value=d}),(d=>{e.status="rejected",e.reason=d})),e)});function s(e,d){const a=l(d),[[r,n,i],s]=(0,o.useReducer)((d=>{const o=a.get(e);return Object.is(d[0],o)&&d[1]===a&&d[2]===e?d:[o,a,e]}),void 0,(()=>[a.get(e),a,e]));let t=r;n===a&&i===e||(s(),t=a.get(e));const b=null==d?void 0:d.delay;return(0,o.useEffect)((()=>{const d=a.sub(e,(()=>{"number"!=typeof b?s():setTimeout(s,b)}));return s(),d}),[a,e,b]),(0,o.useDebugValue)(t),u(t)?c(t):t}function t(e,d){const a=l(d);return(0,o.useCallback)(((...d)=>a.set(e,...d)),[a,e])}function b(e,d){return[s(e,d),t(e,d)]}},565058:(e,d,a)=>{"use strict";a.d(d,{K7:()=>w,MT:()=>A,cn:()=>r});let o=0;function r(e,d){const a="atom"+ ++o,r={toString:()=>a};return"function"==typeof e?r.read=e:(r.init=e,r.read=n,r.write=l),d&&(r.write=d),r}function n(e){return e(this)}function l(e,d,a){return d(this,"function"==typeof a?a(e(this)):a)}const i=(e,d)=>e.unstable_is?e.unstable_is(d):d===e,u=e=>"init"in e,c=e=>!!e.write,s=new WeakMap,t=(e,d)=>{const a=s.get(e);a&&(s.delete(e),a(d))},b=(e,d)=>{e.status="fulfilled",e.value=d},D=(e,d)=>{e.status="rejected",e.reason=d},h=(e,d)=>!!e&&"v"in e&&"v"in d&&Object.is(e.v,d.v),p=(e,d)=>!!e&&"e"in e&&"e"in d&&Object.is(e.e,d.e),S=e=>!!e&&"v"in e&&e.v instanceof Promise,m=e=>{if("e"in e)throw e.e;return e.v},A=()=>{const e=new WeakMap,d=new WeakMap,a=new Map;const o=d=>e.get(d),r=(d,r)=>{const n=o(d);if(e.set(d,r),a.has(d)||a.set(d,n),S(n)){const e="v"in r?r.v instanceof Promise?r.v:Promise.resolve(r.v):Promise.reject(r.e);n.v!==e&&t(n.v,e)}},n=(e,d,a,o)=>{const r=new Map(o?d.d:null);let n=!1;a.forEach(((a,o)=>{!a&&i(e,o)&&(a=d),a&&(r.set(o,a),d.d.get(o)!==a&&(n=!0))})),(n||d.d.size!==r.size)&&(d.d=r)},l=(e,d,a,l)=>{const i=o(e),u={d:(null==i?void 0:i.d)||new Map,v:d};if(a&&n(e,u,a,l),h(i,u)&&i.d===u.d)return i;if(S(i)&&S(u)&&(s=u,"v"in(c=i)&&"v"in s&&c.v.orig&&c.v.orig===s.v.orig)){if(i.d===u.d)return i;u.v=i.v}var c,s;return r(e,u),u},A=(e,a,r,n)=>{if("function"==typeof(null==(i=a)?void 0:i.then)){let i;const u=()=>{const a=o(e);if(!S(a)||a.v!==c)return;const n=l(e,c,r);d.has(e)&&a.d!==n.d&&k(e,n,a.d)},c=new Promise(((e,d)=>{let o=!1;a.then((d=>{o||(o=!0,b(c,d),e(d),u())}),(e=>{o||(o=!0,D(c,e),d(e),u())})),i=d=>{o||(o=!0,d.then((e=>b(c,e)),(e=>D(c,e))),e(d))}}));return c.orig=a,c.status="pending",((e,d)=>{s.set(e,d),e.catch((()=>{})).finally((()=>s.delete(e)))})(c,(e=>{e&&i(e),null==n||n()})),l(e,c,r,!0)}var i;return l(e,a,r)},M=(e,a)=>{const l=o(e);if(!a&&l){if(d.has(e))return l;if(Array.from(l.d).every((([d,a])=>{if(d===e)return!0;const o=M(d);return o===a||h(o,a)})))return l}const s=new Map;let t=!0;const b=d=>{if(i(e,d)){const e=o(d);if(e)return s.set(d,e),m(e);if(u(d))return s.set(d,void 0),d.init;throw new Error("no atom init")}const a=M(d);return s.set(d,a),m(a)};let D,S;const w={get signal(){return D||(D=new AbortController),D.signal},get setSelf(){return!S&&c(e)&&(S=(...d)=>{if(!t)return v(e,...d)}),S}};try{const d=e.read(b,w);return A(e,d,s,(()=>null==D?void 0:D.abort()))}catch(d){return((e,d,a)=>{const l=o(e),i={d:(null==l?void 0:l.d)||new Map,e:d};return a&&n(e,i,a),p(l,i)&&l.d===i.d?l:(r(e,i),i)})(e,d,s)}finally{t=!1}},w=(e,d)=>!d.l.size&&(!d.t.size||1===d.t.size&&d.t.has(e)),y=e=>{const r=new Array,n=new Set,l=e=>{if(!n.has(e)){n.add(e);for(const r of(e=>{var r;const n=new Set(null==(r=d.get(e))?void 0:r.t);return a.forEach(((d,a)=>{var r;(null==(r=o(a))?void 0:r.d.has(e))&&n.add(a)})),n})(e))e!==r&&l(r);r.push(e)}};l(e);const i=new Set([e]);for(let e=r.length-1;e>=0;--e){const d=r[e],a=o(d);if(!a)continue;let n=!1;for(const e of a.d.keys())if(e!==d&&i.has(e)){n=!0;break}if(n){const e=M(d,!0);h(a,e)||i.add(d)}}},f=(e,...d)=>{let a=!0;const r=e.write((e=>m(M(e))),((d,...r)=>{let n;if(i(e,d)){if(!u(d))throw new Error("atom not writable");const e=o(d),a=A(d,r[0]);h(e,a)||y(d)}else n=f(d,...r);if(!a){P();0}return n}),...d);return a=!1,r},v=(e,...d)=>{const a=f(e,...d);P();return a},g=(e,a,r)=>{var n;const l=r||[];null==(n=o(e))||n.d.forEach(((a,o)=>{const r=d.get(o);r?r.t.add(e):o!==e&&g(o,e,l)})),M(e);const i={t:new Set(a&&[a]),l:new Set};if(d.set(e,i),c(e)&&e.onMount){const{onMount:d}=e;l.push((()=>{const a=d(((...d)=>v(e,...d)));a&&(i.u=a)}))}return r||l.forEach((e=>e())),i},_=e=>{var a;const r=null==(a=d.get(e))?void 0:a.u;r&&r(),d.delete(e);const n=o(e);n&&(S(n)&&t(n.v),n.d.forEach(((a,o)=>{if(o!==e){const a=d.get(o);a&&(a.t.delete(e),w(o,a)&&_(o))}})))},k=(e,a,o)=>{const r=new Set(a.d.keys()),n=new Set;null==o||o.forEach(((a,o)=>{if(r.has(o))return void r.delete(o);n.add(o);const l=d.get(o);l&&l.t.delete(e)})),r.forEach((a=>{const o=d.get(a);o?o.t.add(e):d.has(e)&&g(a,e)})),n.forEach((e=>{const a=d.get(e);a&&w(e,a)&&_(e)}))},P=()=>{for(0;a.size;){const e=Array.from(a);a.clear(),e.forEach((([e,a])=>{const r=o(e);if(r){const o=d.get(e);o&&r.d!==(null==a?void 0:a.d)&&k(e,r,null==a?void 0:a.d),o&&(S(a)||!h(a,r)&&!p(a,r))&&o.l.forEach((e=>e()))}else 0}))}};return{get:e=>m(M(e)),set:v,sub:(e,a)=>{const o=(e=>{let a=d.get(e);return a||(a=g(e)),a})(e),r=(P(),o.l);return r.add(a),()=>{r.delete(a),(e=>{const a=d.get(e);a&&w(e,a)&&_(e)})(e)}}}};let M;const w=()=>(M||(M=A()),M)}},e=>{e.O(0,["vendor-38c57b44","vendor-49d0a293","vendor-48a4958c","vendor-3dfac8a4","vendor-cb2d071c","vendor-91c40cd8","vendor-adcb47af","vendor-7940b00b","vendor-669c86db","vendor-3e5eb623","vendor-aaaf2b0c","vendor-c4d1d074","vendor-63e37921","vendor-de539588","vendor-ccf8c62e","vendor-eb8eaa08","vendor-6b20cc7c","vendor-821262ff","vendor-85aa29ea","vendor-e5bca7e4","vendor-49ceb22a","vendor-bfc04956","vendor-e395fecc","vendor-c22f700c","vendor-58c6fc15","vendor-2bf3abf4","vendor-dfe82965","vendor-744bed60","vendor-57216f32","vendor-98a766dd","vendor-ec4c1ee7","vendor-27545368"],(()=>{return d=503287,e(e.s=d);var d}));e.O()}]),window.__SCRIPTS_LOADED__.main=!0);
//# sourceMappingURL=https://ton.local.twitter.com/responsive-web-internal/sourcemaps/client-web/main.e64374ea.js.map