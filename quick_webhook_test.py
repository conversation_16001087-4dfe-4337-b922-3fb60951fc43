#!/usr/bin/env python3
"""
Quick Discord Webhook Test
Simple test to verify the webhook works before running the full monitor
"""

import requests
import json
from datetime import datetime

# Your Discord webhook URL
DISCORD_WEBHOOK_URL = "https://discordapp.com/api/webhooks/1376264613137616976/ouC5Gn0k3pPDhzbR5kIct8YMfpocLsfiQuTK7_xM_b1T1O3zwT-oiP-GzLwzHen8DsjC"

def test_basic_message():
    """Test basic text message"""
    payload = {
        "content": "🤖 Hello! This is a test message from the Tweet Monitor bot.",
        "username": "Tweet Monitor Test"
    }
    
    try:
        response = requests.post(DISCORD_WEBHOOK_URL, json=payload)
        if response.status_code == 204:
            print("✅ Basic message test: SUCCESS")
            return True
        else:
            print(f"❌ Basic message test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Basic message error: {e}")
        return False

def test_embed_message():
    """Test rich embed message (like what tweets will look like)"""
    embed = {
        "title": "🐦 Test Tweet Notification",
        "description": "This is what a real tweet notification will look like! 🚀\n\nIt includes rich formatting, colors, and interactive elements.",
        "color": 0x1DA1F2,  # Twitter blue
        "url": "https://twitter.com/elonmusk/status/123456789",
        "timestamp": datetime.now().isoformat(),
        "fields": [
            {
                "name": "📊 Engagement",
                "value": "💬 42 replies\n🔄 128 retweets\n❤️ 1,337 likes",
                "inline": True
            },
            {
                "name": "⏰ Posted",
                "value": "2 minutes ago",
                "inline": True
            },
            {
                "name": "🔗 Direct Link",
                "value": "[View Tweet](https://twitter.com/elonmusk/status/123456789)",
                "inline": True
            }
        ],
        "footer": {
            "text": "DOM Observer • Real-time Detection",
            "icon_url": "https://abs.twimg.com/icons/apple-touch-icon-192x192.png"
        },
        "thumbnail": {
            "url": "https://abs.twimg.com/icons/apple-touch-icon-192x192.png"
        }
    }
    
    payload = {
        "embeds": [embed],
        "username": "Tweet Monitor",
        "avatar_url": "https://abs.twimg.com/icons/apple-touch-icon-192x192.png"
    }
    
    try:
        response = requests.post(DISCORD_WEBHOOK_URL, json=payload)
        if response.status_code == 204:
            print("✅ Rich embed test: SUCCESS")
            return True
        else:
            print(f"❌ Rich embed test failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Rich embed error: {e}")
        return False

def test_multiple_embeds():
    """Test multiple embeds (like multiple tweets at once)"""
    embeds = []
    
    for i in range(3):
        embed = {
            "title": f"🐦 Test Tweet #{i+1}",
            "description": f"This is test tweet number {i+1}. Testing multiple notifications at once! 🎉",
            "color": 0x1DA1F2,
            "timestamp": datetime.now().isoformat(),
            "fields": [
                {
                    "name": "📊 Stats",
                    "value": f"💬 {i*10} | 🔄 {i*25} | ❤️ {i*100}",
                    "inline": True
                }
            ]
        }
        embeds.append(embed)
    
    payload = {
        "embeds": embeds,
        "username": "Tweet Monitor",
        "content": "📱 Multiple tweets detected simultaneously:"
    }
    
    try:
        response = requests.post(DISCORD_WEBHOOK_URL, json=payload)
        if response.status_code == 204:
            print("✅ Multiple embeds test: SUCCESS")
            return True
        else:
            print(f"❌ Multiple embeds test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Multiple embeds error: {e}")
        return False

def main():
    print("""
🧪 Discord Webhook Test Suite
=============================

Testing your Discord webhook to make sure everything works
before running the full tweet monitor.

Webhook URL: https://discordapp.com/api/webhooks/1376264613137616976/...
    """)
    
    print("🔄 Running webhook tests...\n")
    
    # Test 1: Basic message
    print("1️⃣ Testing basic message...")
    test_basic_message()
    
    print("\n" + "⏳ Waiting 2 seconds between tests...")
    import time
    time.sleep(2)
    
    # Test 2: Rich embed
    print("\n2️⃣ Testing rich embed (tweet-style)...")
    test_embed_message()
    
    print("\n" + "⏳ Waiting 2 seconds between tests...")
    time.sleep(2)
    
    # Test 3: Multiple embeds
    print("\n3️⃣ Testing multiple embeds...")
    test_multiple_embeds()
    
    print("\n" + "="*50)
    print("🎉 Webhook tests completed!")
    print("\nIf you see messages in your Discord channel, the webhook is working!")
    print("You can now run the full tweet monitor with:")
    print("   python discord_webhook_test.py")
    print("\nOr the DOM observer monitor with:")
    print("   python dom_observer_monitor.py")

if __name__ == "__main__":
    main()
