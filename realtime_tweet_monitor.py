#!/usr/bin/env python3
"""
Real-time Tweet Monitor using DOM observation
Monitors X.com for new tweets and can send to Discord/webhooks
"""

import time
import json
import requests
from datetime import datetime
from typing import Set, Dict, Optional, Callable
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC

class TweetMonitor:
    def __init__(self, headless: bool = False):
        self.seen_tweet_ids: Set[str] = set()
        self.driver = None
        self.headless = headless
        self.callbacks = []
        
    def add_callback(self, callback: Callable[[Dict], None]):
        """Add a callback function to handle new tweets"""
        self.callbacks.append(callback)
    
    def setup_driver(self):
        """Setup Chrome driver"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # Basic stealth options
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
    def extract_tweet_data(self, tweet_element, username: str) -> Optional[Dict]:
        """Extract tweet data from DOM element"""
        try:
            # Get tweet text
            try:
                text_element = tweet_element.find_element(By.XPATH, ".//div[@data-testid='tweetText']")
                tweet_text = text_element.text
            except:
                tweet_text = "No text content"
            
            # Get tweet URL and ID
            try:
                link_element = tweet_element.find_element(By.XPATH, ".//a[contains(@href, '/status/')]")
                tweet_url = link_element.get_attribute('href')
                tweet_id = tweet_url.split('/status/')[-1].split('?')[0] if tweet_url else None
            except:
                tweet_url = None
                tweet_id = None
            
            # Get timestamp
            try:
                time_element = tweet_element.find_element(By.XPATH, ".//time")
                timestamp = time_element.get_attribute('datetime')
            except:
                timestamp = None
            
            if tweet_id and tweet_text:
                return {
                    'id': tweet_id,
                    'text': tweet_text,
                    'username': username,
                    'url': tweet_url,
                    'timestamp': timestamp,
                    'detected_at': datetime.now().isoformat()
                }
        except Exception as e:
            print(f"Error extracting tweet: {e}")
            return None
    
    def monitor_user(self, username: str, duration_minutes: int = 60, check_interval: int = 10):
        """Monitor a specific user for new tweets"""
        print(f"🔄 Starting to monitor @{username} for {duration_minutes} minutes...")
        
        try:
            self.setup_driver()
            
            # Navigate to user profile
            self.driver.get(f"https://x.com/{username}")
            
            # Wait for tweets to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.XPATH, "//article[@data-testid='tweet']"))
            )
            
            print(f"✅ Loaded @{username}'s profile")
            
            # Get initial tweets to avoid duplicates
            initial_tweets = self.driver.find_elements(By.XPATH, "//article[@data-testid='tweet']")
            for tweet_element in initial_tweets:
                tweet_data = self.extract_tweet_data(tweet_element, username)
                if tweet_data and tweet_data['id']:
                    self.seen_tweet_ids.add(tweet_data['id'])
            
            print(f"📊 Baseline: {len(self.seen_tweet_ids)} existing tweets")
            
            # Monitor for new tweets
            start_time = time.time()
            end_time = start_time + (duration_minutes * 60)
            
            while time.time() < end_time:
                try:
                    # Refresh the page to get latest tweets
                    self.driver.refresh()
                    
                    # Wait for tweets to load
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, "//article[@data-testid='tweet']"))
                    )
                    
                    # Check for new tweets
                    current_tweets = self.driver.find_elements(By.XPATH, "//article[@data-testid='tweet']")
                    
                    new_tweets_count = 0
                    for tweet_element in current_tweets:
                        tweet_data = self.extract_tweet_data(tweet_element, username)
                        
                        if tweet_data and tweet_data['id'] not in self.seen_tweet_ids:
                            self.seen_tweet_ids.add(tweet_data['id'])
                            new_tweets_count += 1
                            
                            print(f"🆕 NEW TWEET from @{username}:")
                            print(f"   📝 {tweet_data['text']}")
                            print(f"   🔗 {tweet_data['url']}")
                            print(f"   ⏰ {tweet_data['timestamp']}")
                            print("-" * 50)
                            
                            # Call all registered callbacks
                            for callback in self.callbacks:
                                try:
                                    callback(tweet_data)
                                except Exception as e:
                                    print(f"❌ Callback error: {e}")
                    
                    if new_tweets_count == 0:
                        remaining_minutes = (end_time - time.time()) / 60
                        print(f"ℹ️ No new tweets. Checking again in {check_interval}s (remaining: {remaining_minutes:.1f}m)")
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    print(f"⚠️ Error during check: {e}")
                    time.sleep(check_interval)
            
            print(f"✅ Monitoring completed for @{username}")
            
        except Exception as e:
            print(f"❌ Failed to monitor @{username}: {e}")
        finally:
            if self.driver:
                self.driver.quit()

# Callback functions for handling new tweets
def discord_webhook_callback(webhook_url: str):
    """Create a callback that sends tweets to Discord"""
    def callback(tweet_data: Dict):
        embed = {
            "title": f"🐦 New Tweet from @{tweet_data['username']}",
            "description": tweet_data['text'][:2000],
            "color": 0x1DA1F2,
            "url": tweet_data['url'],
            "timestamp": datetime.now().isoformat(),
            "footer": {"text": "X.com Monitor"}
        }
        
        payload = {
            "embeds": [embed],
            "username": "Tweet Monitor"
        }
        
        try:
            response = requests.post(webhook_url, json=payload)
            if response.status_code == 204:
                print(f"✅ Sent to Discord: {tweet_data['text'][:30]}...")
            else:
                print(f"❌ Discord failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Discord error: {e}")
    
    return callback

def file_logger_callback(filename: str = "tweets.jsonl"):
    """Create a callback that logs tweets to a file"""
    def callback(tweet_data: Dict):
        try:
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(json.dumps(tweet_data) + '\n')
            print(f"📝 Logged to {filename}")
        except Exception as e:
            print(f"❌ File logging error: {e}")
    
    return callback

def console_callback(tweet_data: Dict):
    """Simple callback that prints to console"""
    print(f"🔔 ALERT: @{tweet_data['username']} tweeted: {tweet_data['text'][:100]}...")

# Example usage
if __name__ == "__main__":
    # Create monitor
    monitor = TweetMonitor(headless=False)  # Set to True to run without browser window
    
    # Add callbacks
    monitor.add_callback(console_callback)
    monitor.add_callback(file_logger_callback("elon_tweets.jsonl"))
    
    # Uncomment to add Discord webhook
    # DISCORD_WEBHOOK = "https://discord.com/api/webhooks/YOUR_WEBHOOK_URL"
    # monitor.add_callback(discord_webhook_callback(DISCORD_WEBHOOK))
    
    # Monitor a user for 30 minutes, checking every 15 seconds
    monitor.monitor_user("elonmusk", duration_minutes=30, check_interval=15)
