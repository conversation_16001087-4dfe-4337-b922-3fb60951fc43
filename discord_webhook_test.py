#!/usr/bin/env python3
"""
Discord Webhook Test with DOM Observer
Real-time tweet monitoring with instant Discord notifications
"""

import requests
import json
from datetime import datetime
from typing import Dict
from dom_observer_monitor import DOMObserverMonitor

# Your Discord webhook URL
DISCORD_WEBHOOK_URL = "https://discordapp.com/api/webhooks/1376264613137616976/ouC5Gn0k3pPDhzbR5kIct8YMfpocLsfiQuTK7_xM_b1T1O3zwT-oiP-GzLwzHen8DsjC"

def send_test_message():
    """Send a test message to <PERSON>rd to verify webhook works"""
    embed = {
        "title": "🤖 Tweet Monitor Started",
        "description": "DOM Observer monitoring is now active!\nWaiting for new tweets...",
        "color": 0x00ff00,
        "timestamp": datetime.now().isoformat(),
        "footer": {
            "text": "Tweet Monitor Bot",
            "icon_url": "https://abs.twimg.com/icons/apple-touch-icon-192x192.png"
        }
    }
    
    payload = {
        "embeds": [embed],
        "username": "Tweet Monitor",
        "avatar_url": "https://abs.twimg.com/icons/apple-touch-icon-192x192.png"
    }
    
    try:
        response = requests.post(DISCORD_WEBHOOK_URL, json=payload)
        if response.status_code == 204:
            print("✅ Test message sent to Discord successfully!")
            return True
        else:
            print(f"❌ Discord test failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error sending test message: {e}")
        return False

def create_discord_callback():
    """Create Discord webhook callback for tweet notifications"""
    def discord_callback(tweet_data: Dict):
        # Create rich embed for the tweet
        embed = {
            "title": f"🐦 New Tweet from @{tweet_data['username']}",
            "description": tweet_data['text'][:2000],  # Discord limit
            "color": 0x1DA1F2,  # Twitter blue
            "url": tweet_data['url'],
            "timestamp": datetime.now().isoformat(),
            "fields": [
                {
                    "name": "📊 Engagement",
                    "value": f"💬 {tweet_data['replies']} replies\n🔄 {tweet_data['retweets']} retweets\n❤️ {tweet_data['likes']} likes",
                    "inline": True
                },
                {
                    "name": "⏰ Posted",
                    "value": tweet_data['displayTime'],
                    "inline": True
                },
                {
                    "name": "🔗 Direct Link",
                    "value": f"[View Tweet]({tweet_data['url']})",
                    "inline": True
                }
            ],
            "footer": {
                "text": "DOM Observer • Real-time Detection",
                "icon_url": "https://abs.twimg.com/icons/apple-touch-icon-192x192.png"
            }
        }
        
        payload = {
            "embeds": [embed],
            "username": "Tweet Monitor",
            "avatar_url": "https://abs.twimg.com/icons/apple-touch-icon-192x192.png"
        }
        
        try:
            response = requests.post(DISCORD_WEBHOOK_URL, json=payload)
            if response.status_code == 204:
                print(f"✅ Sent to Discord: {tweet_data['text'][:50]}...")
            else:
                print(f"❌ Discord failed: {response.status_code}")
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"❌ Discord error: {e}")
    
    return discord_callback

def console_callback(tweet_data: Dict):
    """Console callback for local monitoring"""
    print(f"\n🚨 NEW TWEET DETECTED! 🚨")
    print(f"👤 User: @{tweet_data['username']}")
    print(f"📝 Text: {tweet_data['text']}")
    print(f"🔗 URL: {tweet_data['url']}")
    print(f"⏰ Time: {tweet_data['displayTime']}")
    print(f"📊 Engagement: 💬 {tweet_data['replies']} | 🔄 {tweet_data['retweets']} | ❤️ {tweet_data['likes']}")
    print("=" * 60)

def main():
    print("""
🚀 Discord Webhook Tweet Monitor Test
====================================

This will monitor Twitter users and send real-time notifications
to your Discord channel using the DOM Observer method.

Features:
⚡ Instant detection (< 1 second)
📱 Rich Discord embeds
📊 Engagement metrics
🔗 Direct tweet links
    """)
    
    # Test Discord webhook first
    print("🔄 Testing Discord webhook...")
    if not send_test_message():
        print("❌ Discord webhook test failed. Please check the URL.")
        return
    
    print("\n✅ Discord webhook is working!")
    
    # Get user input
    username = input("\nEnter Twitter username to monitor (without @): ").strip().lstrip('@')
    if not username:
        username = "elonmusk"  # Default for testing
        print(f"Using default username: {username}")
    
    print(f"\n🎯 Configuration:")
    print(f"   👤 Monitoring: @{username}")
    print(f"   📡 Discord webhook: Active")
    print(f"   ⚡ Method: DOM Observer (instant detection)")
    print(f"   🌐 Browser: Visible (you can watch it work)")
    
    input("\nPress Enter to start monitoring...")
    
    # Create monitor with DOM Observer
    monitor = DOMObserverMonitor(headless=False)  # Set to True to hide browser
    
    # Add callbacks
    monitor.add_callback(console_callback)
    monitor.add_callback(create_discord_callback())
    
    print(f"\n🚀 Starting DOM Observer monitoring for @{username}...")
    print("💡 The browser will open and navigate to the user's profile")
    print("⚡ New tweets will be detected instantly and sent to Discord!")
    print("⏹️ Press Ctrl+C to stop")
    
    try:
        # Start monitoring with very fast checking (0.5 seconds)
        monitor.start_monitoring(username, check_interval=0.5)
        
        # Keep the main thread alive
        while True:
            users = monitor.get_monitored_users()
            if not users:
                break
            
            print(f"📊 Monitoring active for: {', '.join(users)}")
            import time
            time.sleep(30)  # Status update every 30 seconds
        
    except KeyboardInterrupt:
        print(f"\n⏹️ Stopping monitor...")
        monitor.stop_all()
        
        # Send stop notification to Discord
        stop_embed = {
            "title": "🛑 Tweet Monitor Stopped",
            "description": f"Monitoring for @{username} has been stopped.",
            "color": 0xff0000,
            "timestamp": datetime.now().isoformat(),
            "footer": {"text": "Tweet Monitor Bot"}
        }
        
        stop_payload = {
            "embeds": [stop_embed],
            "username": "Tweet Monitor"
        }
        
        try:
            requests.post(DISCORD_WEBHOOK_URL, json=stop_payload)
            print("✅ Sent stop notification to Discord")
        except:
            pass
    
    print("\n👋 Monitor stopped!")

if __name__ == "__main__":
    main()
