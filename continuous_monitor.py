#!/usr/bin/env python3
"""
Continuous Tweet Monitor - Runs indefinitely until stopped
Better approach for Discord bots and long-term monitoring
"""

import time
import threading
from datetime import datetime
from typing import Set, Dict, Optional, Callable, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class ContinuousMonitor:
    def __init__(self, headless: bool = True):
        self.headless = headless
        self.monitoring_threads = {}
        self.callbacks = []
        
    def add_callback(self, callback: Callable[[Dict], None]):
        """Add a callback function to handle new tweets"""
        self.callbacks.append(callback)
    
    def setup_driver(self):
        """Setup Chrome driver"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # Stealth and performance options
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--disable-images")  # Faster loading
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        return driver
    
    def extract_tweet_data(self, tweet_element, username: str) -> Optional[Dict]:
        """Extract tweet data from DOM element"""
        try:
            # Get tweet text
            try:
                text_element = tweet_element.find_element(By.XPATH, ".//div[@data-testid='tweetText']")
                tweet_text = text_element.text
            except:
                tweet_text = "No text content"
            
            # Get tweet URL and ID
            try:
                link_element = tweet_element.find_element(By.XPATH, ".//a[contains(@href, '/status/')]")
                tweet_url = link_element.get_attribute('href')
                tweet_id = tweet_url.split('/status/')[-1].split('?')[0] if tweet_url else None
            except:
                tweet_url = None
                tweet_id = None
            
            # Get timestamp
            try:
                time_element = tweet_element.find_element(By.XPATH, ".//time")
                timestamp = time_element.get_attribute('datetime')
            except:
                timestamp = None
            
            if tweet_id and tweet_text:
                return {
                    'id': tweet_id,
                    'text': tweet_text,
                    'username': username,
                    'url': tweet_url,
                    'timestamp': timestamp,
                    'detected_at': datetime.now().isoformat()
                }
        except Exception as e:
            print(f"Error extracting tweet: {e}")
            return None
    
    def monitor_user_continuous(self, username: str, check_interval: int = 30):
        """Monitor a user continuously until stopped"""
        print(f"🔄 Starting continuous monitoring for @{username} (interval: {check_interval}s)")
        
        driver = None
        seen_tweet_ids: Set[str] = set()
        
        try:
            driver = self.setup_driver()
            
            # Navigate to user profile
            driver.get(f"https://x.com/{username}")
            
            # Wait for tweets to load
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.XPATH, "//article[@data-testid='tweet']"))
            )
            
            print(f"✅ Loaded @{username}'s profile")
            
            # Get initial tweets to avoid duplicates
            initial_tweets = driver.find_elements(By.XPATH, "//article[@data-testid='tweet']")
            for tweet_element in initial_tweets:
                tweet_data = self.extract_tweet_data(tweet_element, username)
                if tweet_data and tweet_data['id']:
                    seen_tweet_ids.add(tweet_data['id'])
            
            print(f"📊 Baseline: {len(seen_tweet_ids)} existing tweets")
            
            # Continuous monitoring loop
            while username in self.monitoring_threads:
                try:
                    # Refresh the page
                    driver.refresh()
                    
                    # Wait for tweets to load
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, "//article[@data-testid='tweet']"))
                    )
                    
                    # Check for new tweets
                    current_tweets = driver.find_elements(By.XPATH, "//article[@data-testid='tweet']")
                    
                    new_tweets_count = 0
                    for tweet_element in current_tweets:
                        tweet_data = self.extract_tweet_data(tweet_element, username)
                        
                        if tweet_data and tweet_data['id'] not in seen_tweet_ids:
                            seen_tweet_ids.add(tweet_data['id'])
                            new_tweets_count += 1
                            
                            print(f"🆕 NEW TWEET from @{username}: {tweet_data['text'][:50]}...")
                            
                            # Call all registered callbacks
                            for callback in self.callbacks:
                                try:
                                    callback(tweet_data)
                                except Exception as e:
                                    print(f"❌ Callback error: {e}")
                    
                    if new_tweets_count == 0:
                        print(f"ℹ️ No new tweets from @{username}. Next check in {check_interval}s")
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    print(f"⚠️ Error during check for @{username}: {e}")
                    time.sleep(check_interval)
            
            print(f"🛑 Stopped monitoring @{username}")
            
        except Exception as e:
            print(f"❌ Failed to monitor @{username}: {e}")
        finally:
            if driver:
                driver.quit()
            # Remove from monitoring threads
            if username in self.monitoring_threads:
                del self.monitoring_threads[username]
    
    def start_monitoring(self, username: str, check_interval: int = 30) -> bool:
        """Start monitoring a user in a separate thread"""
        if username in self.monitoring_threads:
            print(f"⚠️ Already monitoring @{username}")
            return False
        
        # Create and start monitoring thread
        thread = threading.Thread(
            target=self.monitor_user_continuous,
            args=(username, check_interval),
            daemon=True
        )
        
        self.monitoring_threads[username] = thread
        thread.start()
        
        print(f"✅ Started continuous monitoring for @{username}")
        return True
    
    def stop_monitoring(self, username: str) -> bool:
        """Stop monitoring a user"""
        if username not in self.monitoring_threads:
            print(f"⚠️ Not monitoring @{username}")
            return False
        
        # Remove from monitoring threads (this will stop the loop)
        del self.monitoring_threads[username]
        print(f"🛑 Stopping monitoring for @{username}")
        return True
    
    def stop_all(self):
        """Stop monitoring all users"""
        usernames = list(self.monitoring_threads.keys())
        for username in usernames:
            self.stop_monitoring(username)
    
    def get_monitored_users(self) -> List[str]:
        """Get list of currently monitored users"""
        return list(self.monitoring_threads.keys())
    
    def is_monitoring(self, username: str) -> bool:
        """Check if a user is being monitored"""
        return username in self.monitoring_threads

# Example callbacks
def discord_callback(tweet_data: Dict):
    """Example Discord callback"""
    print(f"📡 [DISCORD] New tweet from @{tweet_data['username']}")
    # Here you would send to Discord webhook or bot

def alert_callback(tweet_data: Dict):
    """Example alert callback"""
    print(f"🚨 [ALERT] @{tweet_data['username']}: {tweet_data['text']}")

def log_callback(tweet_data: Dict):
    """Example logging callback"""
    with open(f"tweets_{tweet_data['username']}.log", 'a', encoding='utf-8') as f:
        f.write(f"[{tweet_data['detected_at']}] {tweet_data['text']}\n")
    print(f"📝 [LOG] Saved tweet from @{tweet_data['username']}")

# Example usage
if __name__ == "__main__":
    print("""
🔄 Continuous Twitter Monitor
============================

This monitor runs indefinitely until you stop it.
Perfect for Discord bots and long-term monitoring.

Commands:
- Type 'start <username>' to start monitoring
- Type 'stop <username>' to stop monitoring  
- Type 'status' to see who's being monitored
- Type 'quit' to exit

Example: start elonmusk
    """)
    
    # Create monitor
    monitor = ContinuousMonitor(headless=False)  # Set to True to hide browser
    
    # Add callbacks
    monitor.add_callback(alert_callback)
    monitor.add_callback(log_callback)
    
    # Interactive command loop
    try:
        while True:
            command = input("\n> ").strip().lower()
            
            if command.startswith('start '):
                username = command.split(' ', 1)[1].strip().lstrip('@')
                monitor.start_monitoring(username, check_interval=20)
                
            elif command.startswith('stop '):
                username = command.split(' ', 1)[1].strip().lstrip('@')
                monitor.stop_monitoring(username)
                
            elif command == 'status':
                users = monitor.get_monitored_users()
                if users:
                    print(f"📊 Currently monitoring: {', '.join(users)}")
                else:
                    print("📊 No users being monitored")
                    
            elif command == 'quit':
                print("👋 Stopping all monitoring...")
                monitor.stop_all()
                break
                
            else:
                print("❓ Unknown command. Use: start <user>, stop <user>, status, quit")
                
    except KeyboardInterrupt:
        print("\n👋 Stopping all monitoring...")
        monitor.stop_all()
