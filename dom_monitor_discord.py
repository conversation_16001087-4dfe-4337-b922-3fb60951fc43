#!/usr/bin/env python3
"""
Real-time X.com DOM Monitor with Discord Integration
Monitors X.com pages for new tweets and sends them to Discord
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import time
import json
import asyncio
import aiohttp
from datetime import datetime
from typing import Set, List, Dict, Optional
import threading
import queue

class DiscordWebhook:
    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url
    
    async def send_tweet(self, tweet_data: Dict):
        """Send tweet data to Discord via webhook"""
        embed = {
            "title": f"New Tweet from @{tweet_data['username']}",
            "description": tweet_data['text'][:2000],  # Discord limit
            "color": 0x1DA1F2,  # Twitter blue
            "timestamp": datetime.now().isoformat(),
            "fields": [
                {
                    "name": "🔗 Link",
                    "value": tweet_data.get('url', 'N/A'),
                    "inline": True
                },
                {
                    "name": "⏰ Posted",
                    "value": tweet_data.get('timestamp', 'Unknown'),
                    "inline": True
                }
            ],
            "footer": {
                "text": "X.com Monitor",
                "icon_url": "https://abs.twimg.com/icons/apple-touch-icon-192x192.png"
            }
        }
        
        payload = {
            "embeds": [embed],
            "username": "X.com Monitor",
            "avatar_url": "https://abs.twimg.com/icons/apple-touch-icon-192x192.png"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.webhook_url, json=payload) as response:
                    if response.status == 204:
                        print(f"✅ Sent tweet to Discord: {tweet_data['text'][:50]}...")
                    else:
                        print(f"❌ Discord webhook failed: {response.status}")
        except Exception as e:
            print(f"❌ Error sending to Discord: {e}")

class DOMMonitor:
    def __init__(self, discord_webhook_url: str, headless: bool = False):
        self.discord = DiscordWebhook(discord_webhook_url)
        self.seen_tweet_ids: Set[str] = set()
        self.driver = None
        self.headless = headless
        self.tweet_queue = queue.Queue()
        self.monitoring = False
        
    def setup_driver(self):
        """Setup Chrome driver for DOM monitoring"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # Stealth options
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        # Performance options
        chrome_options.add_argument("--disable-images")  # Don't load images for faster performance
        chrome_options.add_argument("--disable-javascript")  # We'll enable it selectively
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ Browser driver setup complete")
    
    def extract_tweet_from_element(self, tweet_element, username: str) -> Optional[Dict]:
        """Extract tweet data from a DOM element"""
        try:
            # Get tweet text
            text_element = tweet_element.find_element(By.XPATH, ".//div[@data-testid='tweetText']")
            tweet_text = text_element.text if text_element else ""
            
            # Get tweet link and ID
            link_element = tweet_element.find_element(By.XPATH, ".//a[contains(@href, '/status/')]")
            tweet_url = link_element.get_attribute('href')
            tweet_id = tweet_url.split('/status/')[-1].split('?')[0] if tweet_url else None
            
            # Get timestamp
            time_element = tweet_element.find_element(By.XPATH, ".//time")
            timestamp = time_element.get_attribute('datetime') if time_element else None
            
            # Get engagement metrics
            try:
                reply_count = tweet_element.find_element(By.XPATH, ".//div[@data-testid='reply']//span").text
                retweet_count = tweet_element.find_element(By.XPATH, ".//div[@data-testid='retweet']//span").text
                like_count = tweet_element.find_element(By.XPATH, ".//div[@data-testid='like']//span").text
            except:
                reply_count = retweet_count = like_count = "0"
            
            if tweet_id and tweet_text:
                return {
                    'id': tweet_id,
                    'text': tweet_text,
                    'username': username,
                    'url': tweet_url,
                    'timestamp': timestamp,
                    'replies': reply_count,
                    'retweets': retweet_count,
                    'likes': like_count,
                    'detected_at': datetime.now().isoformat()
                }
        except Exception as e:
            print(f"⚠️ Error extracting tweet: {e}")
            return None
    
    def monitor_user_timeline(self, username: str, check_interval: int = 10):
        """Monitor a user's timeline for new tweets"""
        print(f"🔄 Starting to monitor @{username}...")
        
        try:
            # Navigate to user's profile
            self.driver.get(f"https://x.com/{username}")
            
            # Wait for timeline to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//article[@data-testid='tweet']"))
            )
            
            print(f"✅ Loaded timeline for @{username}")
            
            # Initial scan to populate seen tweets
            initial_tweets = self.driver.find_elements(By.XPATH, "//article[@data-testid='tweet']")
            for tweet_element in initial_tweets:
                tweet_data = self.extract_tweet_from_element(tweet_element, username)
                if tweet_data and tweet_data['id']:
                    self.seen_tweet_ids.add(tweet_data['id'])
            
            print(f"📊 Found {len(initial_tweets)} existing tweets")
            
            # Start monitoring loop
            while self.monitoring:
                try:
                    # Scroll to top to check for new tweets
                    self.driver.execute_script("window.scrollTo(0, 0);")
                    time.sleep(2)
                    
                    # Get current tweets
                    current_tweets = self.driver.find_elements(By.XPATH, "//article[@data-testid='tweet']")
                    
                    new_tweets_found = 0
                    for tweet_element in current_tweets:
                        tweet_data = self.extract_tweet_from_element(tweet_element, username)
                        
                        if tweet_data and tweet_data['id'] not in self.seen_tweet_ids:
                            self.seen_tweet_ids.add(tweet_data['id'])
                            self.tweet_queue.put(tweet_data)
                            new_tweets_found += 1
                            print(f"🆕 New tweet detected from @{username}: {tweet_data['text'][:50]}...")
                    
                    if new_tweets_found == 0:
                        print(f"ℹ️ No new tweets from @{username}")
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    print(f"⚠️ Error during monitoring: {e}")
                    time.sleep(check_interval)
                    
        except Exception as e:
            print(f"❌ Failed to monitor @{username}: {e}")
    
    async def discord_sender_loop(self):
        """Async loop to send tweets to Discord"""
        while self.monitoring:
            try:
                # Check for new tweets in queue
                if not self.tweet_queue.empty():
                    tweet_data = self.tweet_queue.get()
                    await self.discord.send_tweet(tweet_data)
                
                await asyncio.sleep(1)  # Check queue every second
                
            except Exception as e:
                print(f"❌ Error in Discord sender: {e}")
                await asyncio.sleep(5)
    
    def start_monitoring(self, usernames: List[str], check_interval: int = 30):
        """Start monitoring multiple users"""
        print(f"🚀 Starting DOM monitoring for users: {usernames}")
        print(f"⏱️ Check interval: {check_interval} seconds")
        
        self.monitoring = True
        self.setup_driver()
        
        try:
            # Start Discord sender in background
            discord_loop = asyncio.new_event_loop()
            discord_thread = threading.Thread(
                target=lambda: discord_loop.run_until_complete(self.discord_sender_loop())
            )
            discord_thread.daemon = True
            discord_thread.start()
            
            # Monitor each user (for now, just monitor one at a time)
            # In a full implementation, you'd want multiple browser instances
            for username in usernames:
                print(f"\n📱 Switching to monitor @{username}")
                self.monitor_user_timeline(username, check_interval)
                
        except KeyboardInterrupt:
            print("\n👋 Stopping monitor...")
        finally:
            self.monitoring = False
            if self.driver:
                self.driver.quit()

# Example usage
if __name__ == "__main__":
    # Replace with your Discord webhook URL
    DISCORD_WEBHOOK_URL = "https://discord.com/api/webhooks/YOUR_WEBHOOK_URL_HERE"
    
    # Users to monitor
    users_to_monitor = ["elonmusk", "OpenAI"]
    
    # Create monitor
    monitor = DOMMonitor(DISCORD_WEBHOOK_URL, headless=False)
    
    # Start monitoring
    monitor.start_monitoring(users_to_monitor, check_interval=15)
