import re
import requests
from typing import <PERSON><PERSON>, Optional
import time
import random

def scrape_bundle_data() -> <PERSON><PERSON>[str, str, str, str]:
    """
    Scrapes the X.com HTML, finds the main JS bundle URL, downloads it,
    and extracts the GraphQL endpoint, guest bearer token, and operation names.

    Raises:
        Exception: If any required data cannot be extracted
    """

    # Headers to mimic a real browser request
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
    }

    try:
        # Add a small delay to avoid being too aggressive
        time.sleep(random.uniform(1, 3))

        print("Fetching X.com homepage...")
        response = requests.get("https://x.com", headers=headers, timeout=30)
        response.raise_for_status()

        print("Searching for main JS bundle...")
        # Try multiple patterns for finding the main bundle
        bundle_patterns = [
            r'<script[^>]*src="([^"]*(main\.[^"]*\.js))"',
            r'<script[^>]*src="([^"]*main[^"]*\.js)"',
            r'"src":"([^"]*main[^"]*\.js)"'
        ]

        bundle_url = None
        for pattern in bundle_patterns:
            script_tag_match = re.search(pattern, response.text)
            if script_tag_match:
                bundle_url = script_tag_match.group(1)
                # Ensure the URL is absolute
                if bundle_url.startswith('//'):
                    bundle_url = 'https:' + bundle_url
                elif bundle_url.startswith('/'):
                    bundle_url = 'https://x.com' + bundle_url
                break

        if not bundle_url:
            raise ValueError("Could not find main JS bundle URL")

        print(f"Found bundle URL: {bundle_url}")

        # Add another delay before fetching the bundle
        time.sleep(random.uniform(1, 2))

        print("Fetching JS bundle...")
        bundle_response = requests.get(bundle_url, headers=headers, timeout=30)
        bundle_response.raise_for_status()
        bundle_text = bundle_response.text

        print("Extracting GraphQL data...")
        print(f"Bundle size: {len(bundle_text):,} characters")

        # Simpler, more efficient regex patterns
        graphql_patterns = [
            r"(https://api-p\d+\.x\.com/graphql/[^\"\\]+)",
            r"GraphQL[^\"]*url[^\"]*?(https://api[^\"]*x\.com/graphql/[^\"]+)",
            r"(https://api\.twitter\.com/graphql/[^\"\\]+)",
            r"(https://[^\"]*(?:twitter|x)\.com[^\"]*?/graphql[^\"]*)"
        ]

        bearer_patterns = [
            # Look for Bearer token patterns in minified code
            r"Bearer[^\"']*[\"']([A-Za-z0-9%]{30,})[\"']",
            r"guest[^\"']*token[^\"']*[\"']([A-Za-z0-9%]{30,})[\"']",
            r"BEARER_TOKEN[\"']?\s*[:=]\s*[\"']([A-Za-z0-9%]{30,})[\"']",
            r"token[\"']?\s*[:=]\s*[\"']([A-Za-z0-9%]{30,})[\"']",
            # New patterns for minified code
            r"[\"']([A-Za-z0-9%]{100,120})[\"']",  # Long alphanumeric strings (typical bearer token length)
            r"[\"'](AAAA[A-Za-z0-9%]{100,})[\"']",  # Twitter bearer tokens often start with AAAA
            r"[\"'](Bearer\s+[A-Za-z0-9%]{100,})[\"']",  # Full Bearer header
            r"authorization[\"']?\s*[:=]\s*[\"']([^\"']{100,})[\"']",  # Authorization header
            r"[\"']([A-Za-z0-9%]{80,150})[\"']",  # Broader range for tokens
            # Look for base64-like patterns (bearer tokens are often base64)
            r"[\"']([A-Za-z0-9+/]{100,}={0,2})[\"']",
        ]

        query_patterns = [
            r"UserByScreenName[^\"']*[\"']([A-Za-z0-9]{15,})[\"']",
            r"UserByScreenName[^{}]*id[\"']?\s*[:=]\s*[\"']([A-Za-z0-9]{15,})[\"']",
            r"UserByScreenName.*?queryId[\"']?\s*[:=]\s*[\"']([A-Za-z0-9]{15,})[\"']"
        ]

        tweets_patterns = [
            r"UserTweets[^\"']*[\"']([A-Za-z0-9]{15,})[\"']",
            r"UserTweets[^{}]*id[\"']?\s*[:=]\s*[\"']([A-Za-z0-9]{15,})[\"']",
            r"UserTweetsAndReplies.*?queryId[\"']?\s*[:=]\s*[\"']([A-Za-z0-9]{15,})[\"']"
        ]

        # Try to find matches using simpler patterns with timeout protection
        graphql_endpoint: Optional[str] = None
        for i, pattern in enumerate(graphql_patterns):
            print(f"  Trying pattern {i+1}/{len(graphql_patterns)}...")
            match = re.search(pattern, bundle_text, re.IGNORECASE)
            if match:
                graphql_endpoint = match.group(1)
                print(f"  Found GraphQL endpoint!")
                break

        # If GraphQL endpoint still not found, try a broader search on full bundle
        if not graphql_endpoint:
            print("Trying broader search for any X.com API URLs in full bundle...")
            # Search for common GraphQL endpoint patterns
            broad_patterns = [
                r'(https://[^"]*api[^"]*graphql[^"]*)',
                r'(https://[^"]*twitter\.com[^"]*graphql[^"]*)',
                r'(https://[^"]*x\.com[^"]*graphql[^"]*)',
                r'"(https://api[^"]*\.(?:twitter\.com|x\.com)[^"]*)"'
            ]

            for pattern in broad_patterns:
                matches = re.findall(pattern, bundle_text, re.IGNORECASE)
                if matches:
                    graphql_endpoint = matches[0]
                    print(f"  Found GraphQL URL via broad search: {graphql_endpoint}")
                    break

        print("Searching for bearer token...")
        bearer_token: Optional[str] = None
        for i, pattern in enumerate(bearer_patterns):
            print(f"  Trying pattern {i+1}/{len(bearer_patterns)}...")
            match = re.search(pattern, bundle_text, re.IGNORECASE)
            if match:
                bearer_token = match.group(1)
                print(f"  Found bearer token! Length: {len(bearer_token) if bearer_token else 0}")
                break

        # If no bearer token found, try to find any long strings that might be tokens
        if not bearer_token:
            print("  No bearer token found with standard patterns, searching for long strings...")
            # Look for any string longer than 80 characters that might be a token
            long_string_pattern = r"[\"']([A-Za-z0-9+/=%]{80,200})[\"']"
            matches = re.findall(long_string_pattern, bundle_text)
            if matches:
                print(f"  Found {len(matches)} long strings, examining...")
                for i, match in enumerate(matches[:10]):  # Check first 10 matches
                    print(f"    String {i+1}: {match[:50]}... (length: {len(match)})")
                    # Use the first one that looks like a bearer token
                    if len(match) > 100 and not match.startswith('http'):
                        bearer_token = match
                        print(f"  Using long string as potential bearer token!")
                        break

        print("Searching for UserByScreenName query...")
        user_by_screen_name_query: Optional[str] = None
        for i, pattern in enumerate(query_patterns):
            print(f"  Trying pattern {i+1}/{len(query_patterns)}...")
            match = re.search(pattern, bundle_text, re.IGNORECASE)
            if match:
                user_by_screen_name_query = match.group(1)
                print(f"  Found UserByScreenName query!")
                break

        print("Searching for UserTweets query...")
        user_tweets_stream: Optional[str] = None
        for i, pattern in enumerate(tweets_patterns):
            print(f"  Trying pattern {i+1}/{len(tweets_patterns)}...")
            match = re.search(pattern, bundle_text, re.IGNORECASE)
            if match:
                user_tweets_stream = match.group(1)
                print(f"  Found UserTweets query!")
                break

        # Check what we found
        found_items = []
        if graphql_endpoint:
            found_items.append("GraphQL endpoint")
        if bearer_token:
            found_items.append("Bearer token")
        if user_by_screen_name_query:
            found_items.append("UserByScreenNameQuery")
        if user_tweets_stream:
            found_items.append("UserTweetsStream")

        print(f"Found: {', '.join(found_items) if found_items else 'None'}")

        # Only require bearer token and query IDs - GraphQL endpoint can be fallback
        required_items = [bearer_token, user_by_screen_name_query, user_tweets_stream]
        if not all(required_items):
            missing = []
            if not bearer_token:
                missing.append("Bearer token")
            if not user_by_screen_name_query:
                missing.append("UserByScreenNameQuery")
            if not user_tweets_stream:
                missing.append("UserTweetsStream")

            print(f"Missing critical items: {', '.join(missing)}")
            print("Bundle size:", len(bundle_text), "characters")

            # Optionally save bundle for manual inspection
            print("Saving bundle sample for inspection...")
            with open('bundle_debug.js', 'w', encoding='utf-8') as f:
                f.write(bundle_text[-50000:])  # Last 50k chars - API config often at end
            print("Saved last 50k characters to bundle_debug.js for inspection")

            raise ValueError(f"Could not extract critical data. Missing: {', '.join(missing)}")

        # Use fallback endpoint if not found
        if not graphql_endpoint:
            fallback_endpoints = [
                "https://twitter.com/i/api/graphql",
                "https://x.com/i/api/graphql",
                "https://api.twitter.com/graphql",
                "https://api.x.com/graphql"
            ]
            print(f"Using fallback GraphQL endpoint: {fallback_endpoints[0]}")
            graphql_endpoint = fallback_endpoints[0]

        # At this point, all values are guaranteed to be strings, not None
        # Type assertion to satisfy Pylance
        assert graphql_endpoint is not None
        assert bearer_token is not None
        assert user_by_screen_name_query is not None
        assert user_tweets_stream is not None

        return graphql_endpoint, bearer_token, user_by_screen_name_query, user_tweets_stream

    except requests.exceptions.RequestException as e:
        if hasattr(e, 'response') and e.response is not None:
            print(f"HTTP Status: {e.response.status_code}")
            print(f"Response headers: {dict(e.response.headers)}")
            if e.response.text:
                print(f"Response body (first 500 chars): {e.response.text[:500]}")
        raise Exception(f"Request error: {e}")
    except ValueError as e:
        raise Exception(f"Error extracting data: {e}")
    except Exception as e:
        raise Exception(f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    try:
        graphql_endpoint, bearer_token, user_by_screen_name_query, user_tweets_stream = scrape_bundle_data()
        print("\n" + "="*50)
        print("RESULTS:")
        print("="*50)
        print(f"GraphQL Endpoint: {graphql_endpoint}")
        print(f"Bearer Token: {bearer_token}")
        print(f"UserByScreenNameQuery: {user_by_screen_name_query}")
        print(f"UserTweetsStream: {user_tweets_stream}")
    except Exception as e:
        print(f"Error: {e}")
