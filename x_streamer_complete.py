#!/usr/bin/env python3
"""
Complete X.com Tweet Streamer
Combines bootstrap, resolver, and streaming functionality
"""

import json
import requests
import time
from datetime import datetime
from typing import List, Dict, Set, Tuple, Optional
import re
import random

class XStreamer:
    def __init__(self):
        self.graphql_endpoint = None
        self.bearer_token = None
        self.user_by_screen_name_query = None
        self.user_tweets_stream = None
        self.seen_tweet_ids: Set[str] = set()
        self.handle_to_user_id_cache: Dict[str, str] = {}

    def bootstrap(self) -> bool:
        """
        Extract API credentials from X.com
        """
        print("🔄 Bootstrapping X.com API credentials...")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }
        
        try:
            time.sleep(random.uniform(1, 3))
            
            print("  Fetching X.com homepage...")
            response = requests.get("https://x.com", headers=headers, timeout=30)
            response.raise_for_status()
            
            print("  Searching for main JS bundle...")
            bundle_patterns = [
                r'<script[^>]*src="([^"]*(main\.[^"]*\.js))"',
                r'<script[^>]*src="([^"]*main[^"]*\.js)"',
                r'"src":"([^"]*main[^"]*\.js)"'
            ]
            
            bundle_url = None
            for pattern in bundle_patterns:
                script_tag_match = re.search(pattern, response.text)
                if script_tag_match:
                    bundle_url = script_tag_match.group(1)
                    if bundle_url.startswith('//'):
                        bundle_url = 'https:' + bundle_url
                    elif bundle_url.startswith('/'):
                        bundle_url = 'https://x.com' + bundle_url
                    break

            if not bundle_url:
                print("  ❌ Could not find main JS bundle URL")
                return False
            
            print(f"  Found bundle URL: {bundle_url}")
            
            time.sleep(random.uniform(1, 2))
            
            print("  Fetching JS bundle...")
            bundle_response = requests.get(bundle_url, headers=headers, timeout=30)
            bundle_response.raise_for_status()
            bundle_text = bundle_response.text
            
            print(f"  Bundle size: {len(bundle_text):,} characters")
            
            # Extract GraphQL endpoint
            graphql_patterns = [
                r"(https://api-p\d+\.x\.com/graphql/[^\"\\]+)",
                r"(https://api\.twitter\.com/graphql/[^\"\\]+)",
                r"(https://[^\"]*(?:twitter|x)\.com[^\"]*?/graphql[^\"]*)"
            ]
            
            for pattern in graphql_patterns:
                match = re.search(pattern, bundle_text, re.IGNORECASE)
                if match:
                    self.graphql_endpoint = match.group(1)
                    break
                    
            if not self.graphql_endpoint:
                # Use fallback
                self.graphql_endpoint = "https://api.twitter.com/graphql"
                print(f"  Using fallback GraphQL endpoint: {self.graphql_endpoint}")
            
            # Extract bearer token
            bearer_patterns = [
                r"Bearer[^\"']*[\"']([A-Za-z0-9%]{30,})[\"']",
                r"[\"']([A-Za-z0-9%]{100,120})[\"']",
                r"[\"'](AAAA[A-Za-z0-9%]{100,})[\"']",
                r"[\"'](Bearer\s+[A-Za-z0-9%]{100,})[\"']",
                r"authorization[\"']?\s*[:=]\s*[\"']([^\"']{100,})[\"']",
                r"[\"']([A-Za-z0-9%]{80,150})[\"']",
                r"[\"']([A-Za-z0-9+/]{100,}={0,2})[\"']",
            ]
            
            for pattern in bearer_patterns:
                match = re.search(pattern, bundle_text, re.IGNORECASE)
                if match:
                    token = match.group(1)
                    if not token.startswith('Bearer '):
                        token = f"Bearer {token}"
                    self.bearer_token = token
                    break
            
            # Extract query IDs
            query_patterns = [
                r"UserByScreenName[^\"']*[\"']([A-Za-z0-9]{15,})[\"']",
                r"UserByScreenName[^{}]*id[\"']?\s*[:=]\s*[\"']([A-Za-z0-9]{15,})[\"']",
                r"UserByScreenName.*?queryId[\"']?\s*[:=]\s*[\"']([A-Za-z0-9]{15,})[\"']"
            ]
            
            for pattern in query_patterns:
                match = re.search(pattern, bundle_text, re.IGNORECASE)
                if match:
                    self.user_by_screen_name_query = match.group(1)
                    break
            
            tweets_patterns = [
                r"UserTweets[^\"']*[\"']([A-Za-z0-9]{15,})[\"']",
                r"UserTweets[^{}]*id[\"']?\s*[:=]\s*[\"']([A-Za-z0-9]{15,})[\"']",
                r"UserTweetsAndReplies.*?queryId[\"']?\s*[:=]\s*[\"']([A-Za-z0-9]{15,})[\"']"
            ]
            
            for pattern in tweets_patterns:
                match = re.search(pattern, bundle_text, re.IGNORECASE)
                if match:
                    self.user_tweets_stream = match.group(1)
                    break
            
            # Check if we have all required data
            if all([self.graphql_endpoint, self.bearer_token, self.user_by_screen_name_query, self.user_tweets_stream]):
                print("  ✅ Successfully extracted all API credentials!")
                return True
            else:
                missing = []
                if not self.graphql_endpoint:
                    missing.append("GraphQL endpoint")
                if not self.bearer_token:
                    missing.append("Bearer token")
                if not self.user_by_screen_name_query:
                    missing.append("UserByScreenName query")
                if not self.user_tweets_stream:
                    missing.append("UserTweets query")
                
                print(f"  ❌ Missing: {', '.join(missing)}")
                return False
                
        except Exception as e:
            print(f"  ❌ Bootstrap failed: {e}")
            return False

    def resolve_user_ids(self, handles: List[str]) -> Dict[str, str]:
        """
        Resolve Twitter handles to user IDs
        """
        print(f"🔄 Resolving user IDs for handles: {handles}")
        
        resolved_ids: Dict[str, str] = {}
        
        for handle in handles:
            if handle in self.handle_to_user_id_cache:
                resolved_ids[handle] = self.handle_to_user_id_cache[handle]
                print(f"  ✅ {handle} -> {self.handle_to_user_id_cache[handle]} (cached)")
                continue
            
            try:
                # Note: This is a simplified approach. The actual GraphQL query structure
                # may be different and require additional parameters
                payload = {
                    "variables": {"screen_name": handle},
                    "queryId": self.user_by_screen_name_query
                }
                
                headers = {
                    "Authorization": self.bearer_token,
                    "Content-Type": "application/json",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "x-twitter-auth-type": "OAuth2Session",
                    "x-twitter-client-language": "en",
                    "x-twitter-active-user": "yes",
                }
                
                response = requests.post(f"{self.graphql_endpoint}/{self.user_by_screen_name_query}", 
                                       headers=headers, 
                                       data=json.dumps(payload),
                                       timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    # Parse response to extract user ID
                    # Note: The actual response structure may vary
                    if "data" in data and "user" in data["data"]:
                        user_id = data["data"]["user"]["result"]["rest_id"]
                        self.handle_to_user_id_cache[handle] = user_id
                        resolved_ids[handle] = user_id
                        print(f"  ✅ {handle} -> {user_id}")
                    else:
                        print(f"  ❌ Could not resolve {handle}: Unexpected response structure")
                else:
                    print(f"  ❌ Could not resolve {handle}: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Error resolving {handle}: {e}")
        
        return resolved_ids

    def get_user_tweets(self, user_id: str, count: int = 20) -> List[Dict]:
        """
        Fetch recent tweets for a specific user
        """
        # This is a simplified implementation
        # The actual GraphQL query may require different parameters
        print(f"  Fetching tweets for user {user_id}...")
        return []  # Placeholder - actual implementation would make GraphQL request

    def start_streaming(self, handles: List[str], interval: int = 30):
        """
        Start streaming tweets for the given handles
        """
        print("🚀 Starting X.com Tweet Streamer")
        
        # Step 1: Bootstrap
        if not self.bootstrap():
            print("❌ Failed to bootstrap. Exiting.")
            return
        
        # Step 2: Resolve user IDs
        user_ids = self.resolve_user_ids(handles)
        if not user_ids:
            print("❌ No user IDs resolved. Exiting.")
            return
        
        # Step 3: Start polling for tweets
        print(f"🔄 Starting to poll tweets every {interval} seconds...")
        print("Press Ctrl+C to stop")
        
        try:
            while True:
                for handle, user_id in user_ids.items():
                    tweets = self.get_user_tweets(user_id)
                    # Process new tweets here
                    if tweets:
                        print(f"📱 New tweets from @{handle}:")
                        for tweet in tweets:
                            print(f"  {tweet.get('text', 'No text')}")
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n👋 Stopping streamer...")

if __name__ == "__main__":
    streamer = XStreamer()
    
    # Example usage
    handles_to_track = ["elonmusk", "OpenAI"]  # Replace with handles you want to track
    
    streamer.start_streaming(handles_to_track, interval=60)  # Poll every 60 seconds
