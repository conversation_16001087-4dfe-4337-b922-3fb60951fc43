# detailed_search.py
import re

with open('bundle_debug.js', 'r', encoding='utf-8') as f:
    content = f.read()

print(f"Bundle debug file size: {len(content):,} characters")
print("="*60)

# Search for GraphQL-related terms
print("=== Searching for 'graphql' (case insensitive) ===")
graphql_matches = re.findall(r'.{0,50}graphql.{0,50}', content, re.IGNORECASE)
for i, match in enumerate(graphql_matches[:10]):
    print(f"{i+1}: ...{match}...")

print("\n=== Searching for 'api-p' (API prefix) ===")
api_p_matches = re.findall(r'.{0,30}api-p.{0,30}', content, re.IGNORECASE)
for i, match in enumerate(api_p_matches[:10]):
    print(f"{i+1}: ...{match}...")

print("\n=== Searching for URL patterns ===")
# Look for https:// followed by domain patterns
url_patterns = re.findall(r'https://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}[^"\s]*', content)
for i, url in enumerate(url_patterns[:15]):
    print(f"{i+1}: {url}")

print("\n=== Searching for domain fragments ===")
# Look for x.com or twitter.com references
domain_matches = re.findall(r'.{0,30}(?:x\.com|twitter\.com).{0,30}', content, re.IGNORECASE)
for i, match in enumerate(domain_matches[:10]):
    print(f"{i+1}: ...{match}...")

print("\n=== Searching for 'endpoint' or 'url' ===")
endpoint_matches = re.findall(r'.{0,40}(?:endpoint|url).{0,40}', content, re.IGNORECASE)
for i, match in enumerate(endpoint_matches[:10]):
    print(f"{i+1}: ...{match}...")

print("\n=== Searching for base64 or encoded strings ===")
# Sometimes endpoints are base64 encoded
encoded_matches = re.findall(r'[A-Za-z0-9+/]{40,}={0,2}', content)
for i, match in enumerate(encoded_matches[:5]):
    print(f"{i+1}: {match[:60]}...")
    # Try to decode
    try:
        import base64
        decoded = base64.b64decode(match + '==').decode('utf-8', errors='ignore')
        if 'http' in decoded or 'api' in decoded:
            print(f"    Decoded: {decoded}")
    except:
        pass

print("\n=== Raw content sample (first 500 chars) ===")
print(content[:500])