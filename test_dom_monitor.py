#!/usr/bin/env python3
"""
Test script for DOM monitoring
Simple demonstration of real-time tweet detection
"""

import time
from datetime import datetime
from realtime_tweet_monitor import TweetM<PERSON>tor

def simple_alert_callback(tweet_data):
    """Simple callback that shows an alert-style message"""
    print("\n" + "="*60)
    print("🚨 NEW TWEET DETECTED! 🚨")
    print("="*60)
    print(f"👤 User: @{tweet_data['username']}")
    print(f"📝 Text: {tweet_data['text']}")
    print(f"🔗 URL: {tweet_data['url']}")
    print(f"⏰ Time: {tweet_data['timestamp']}")
    print(f"🕐 Detected: {tweet_data['detected_at']}")
    print("="*60)
    
    # You could add more actions here:
    # - Send to Discord webhook
    # - Save to database
    # - Send email notification
    # - Trigger other automations

def webhook_simulator_callback(tweet_data):
    """Simulate sending to a webhook"""
    print(f"📡 [WEBHOOK] Sending tweet to external service...")
    print(f"    Data: {tweet_data['username']} - {tweet_data['text'][:50]}...")
    # In real implementation, you'd make an HTTP request here

def file_logger_callback(tweet_data):
    """Log tweets to a file"""
    filename = f"tweets_{tweet_data['username']}.log"
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    with open(filename, 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] @{tweet_data['username']}: {tweet_data['text']}\n")
    
    print(f"📝 Logged tweet to {filename}")

def main():
    print("""
🔍 X.com DOM Monitor Test
========================

This script will monitor a Twitter user's profile for new tweets in real-time.
When a new tweet is detected, it will trigger various callbacks.

Features demonstrated:
✅ Real-time DOM monitoring
✅ Tweet detection and extraction
✅ Multiple callback handlers
✅ Data logging and alerts

Note: This requires Chrome/Chromium and ChromeDriver to be installed.
    """)
    
    # Get user input
    username = input("Enter Twitter username to monitor (without @): ").strip().lstrip('@')
    if not username:
        username = "elonmusk"  # Default for testing
        print(f"Using default username: {username}")
    
    duration = input("Monitor duration in minutes (default 5): ").strip()
    try:
        duration = int(duration) if duration else 5
    except ValueError:
        duration = 5
        print("Invalid duration, using 5 minutes")
    
    check_interval = input("Check interval in seconds (default 15): ").strip()
    try:
        check_interval = int(check_interval) if check_interval else 15
    except ValueError:
        check_interval = 15
        print("Invalid interval, using 15 seconds")
    
    print(f"\n🎯 Configuration:")
    print(f"   👤 User: @{username}")
    print(f"   ⏱️ Duration: {duration} minutes")
    print(f"   🔄 Check interval: {check_interval} seconds")
    print(f"   🌐 Browser: Visible (set headless=True to hide)")
    
    input("\nPress Enter to start monitoring...")
    
    # Create monitor
    monitor = TweetMonitor(headless=False)  # Set to True to hide browser
    
    # Add multiple callbacks
    monitor.add_callback(simple_alert_callback)
    monitor.add_callback(webhook_simulator_callback)
    monitor.add_callback(file_logger_callback)
    
    print(f"\n🚀 Starting to monitor @{username}...")
    print("💡 Tip: Try posting a tweet from another account to test detection!")
    print("⏹️ Press Ctrl+C to stop early")
    
    try:
        # Start monitoring
        monitor.monitor_user(username, duration_minutes=duration, check_interval=check_interval)
        print(f"\n✅ Monitoring completed for @{username}")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ Monitoring stopped by user")
    except Exception as e:
        print(f"\n❌ Error during monitoring: {e}")
    
    print("\n👋 Monitor test completed!")

if __name__ == "__main__":
    main()
