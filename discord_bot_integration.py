#!/usr/bin/env python3
"""
Discord Bot Integration for X.com Tweet Monitoring
Combines real-time tweet monitoring with Discord bot functionality
"""

import discord
from discord.ext import commands, tasks
import asyncio
import json
import threading
import queue
from datetime import datetime
from typing import Dict, Set
from realtime_tweet_monitor import TweetMonitor

class TwitterDiscordBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        super().__init__(command_prefix='!', intents=intents)
        
        self.tweet_queue = queue.Queue()
        self.monitored_users = set()
        self.monitor_threads = {}
        self.tweet_channel_id = None  # Set this to your Discord channel ID
        
    async def on_ready(self):
        print(f'🤖 <PERSON><PERSON> logged in as {self.user}')
        self.process_tweets.start()  # Start the tweet processing loop
    
    @tasks.loop(seconds=1)
    async def process_tweets(self):
        """Process tweets from the queue and send to Discord"""
        while not self.tweet_queue.empty():
            try:
                tweet_data = self.tweet_queue.get_nowait()
                await self.send_tweet_to_discord(tweet_data)
            except queue.Empty:
                break
            except Exception as e:
                print(f"❌ Error processing tweet: {e}")
    
    async def send_tweet_to_discord(self, tweet_data: Dict):
        """Send tweet data to Discord channel"""
        if not self.tweet_channel_id:
            print("⚠️ No tweet channel ID set")
            return
        
        channel = self.get_channel(self.tweet_channel_id)
        if not channel:
            print(f"❌ Could not find channel {self.tweet_channel_id}")
            return
        
        # Create embed
        embed = discord.Embed(
            title=f"🐦 New Tweet from @{tweet_data['username']}",
            description=tweet_data['text'][:2000],  # Discord limit
            color=0x1DA1F2,  # Twitter blue
            url=tweet_data['url'],
            timestamp=datetime.now()
        )
        
        embed.add_field(
            name="⏰ Posted",
            value=tweet_data.get('timestamp', 'Unknown'),
            inline=True
        )
        
        embed.set_footer(text="X.com Monitor")
        
        try:
            await channel.send(embed=embed)
            print(f"✅ Sent tweet to Discord: {tweet_data['text'][:50]}...")
        except Exception as e:
            print(f"❌ Failed to send to Discord: {e}")
    
    def tweet_callback(self, tweet_data: Dict):
        """Callback function for the tweet monitor"""
        self.tweet_queue.put(tweet_data)
    
    def start_monitoring_user(self, username: str, duration_minutes: int = 1440):  # 24 hours default
        """Start monitoring a user in a separate thread"""
        if username in self.monitored_users:
            print(f"⚠️ Already monitoring @{username}")
            return False
        
        def monitor_thread():
            monitor = TweetMonitor(headless=True)
            monitor.add_callback(self.tweet_callback)
            monitor.monitor_user(username, duration_minutes=duration_minutes, check_interval=30)
            
            # Remove from monitored users when done
            self.monitored_users.discard(username)
            if username in self.monitor_threads:
                del self.monitor_threads[username]
        
        thread = threading.Thread(target=monitor_thread, daemon=True)
        thread.start()
        
        self.monitored_users.add(username)
        self.monitor_threads[username] = thread
        
        print(f"✅ Started monitoring @{username}")
        return True
    
    def stop_monitoring_user(self, username: str):
        """Stop monitoring a user"""
        if username not in self.monitored_users:
            return False
        
        # Note: This is a simplified stop mechanism
        # In a production system, you'd want proper thread management
        self.monitored_users.discard(username)
        if username in self.monitor_threads:
            del self.monitor_threads[username]
        
        print(f"🛑 Stopped monitoring @{username}")
        return True
    
    @commands.command(name='monitor')
    async def monitor_command(self, ctx, username: str, duration: int = 60):
        """Start monitoring a Twitter user
        Usage: !monitor elonmusk 120 (monitor for 120 minutes)
        """
        if not username.startswith('@'):
            username = username.lstrip('@')  # Remove @ if present
        
        if self.start_monitoring_user(username, duration):
            embed = discord.Embed(
                title="📡 Monitoring Started",
                description=f"Now monitoring @{username} for {duration} minutes",
                color=0x00ff00
            )
            await ctx.send(embed=embed)
        else:
            embed = discord.Embed(
                title="⚠️ Already Monitoring",
                description=f"@{username} is already being monitored",
                color=0xffaa00
            )
            await ctx.send(embed=embed)
    
    @commands.command(name='stop')
    async def stop_command(self, ctx, username: str):
        """Stop monitoring a Twitter user
        Usage: !stop elonmusk
        """
        username = username.lstrip('@')
        
        if self.stop_monitoring_user(username):
            embed = discord.Embed(
                title="🛑 Monitoring Stopped",
                description=f"Stopped monitoring @{username}",
                color=0xff0000
            )
            await ctx.send(embed=embed)
        else:
            embed = discord.Embed(
                title="❌ Not Monitoring",
                description=f"@{username} is not being monitored",
                color=0xff0000
            )
            await ctx.send(embed=embed)
    
    @commands.command(name='status')
    async def status_command(self, ctx):
        """Show monitoring status"""
        if not self.monitored_users:
            embed = discord.Embed(
                title="📊 Monitoring Status",
                description="No users currently being monitored",
                color=0x808080
            )
        else:
            users_list = "\n".join([f"• @{user}" for user in self.monitored_users])
            embed = discord.Embed(
                title="📊 Monitoring Status",
                description=f"Currently monitoring {len(self.monitored_users)} users:\n{users_list}",
                color=0x1DA1F2
            )
        
        await ctx.send(embed=embed)
    
    @commands.command(name='setchannel')
    async def set_channel_command(self, ctx):
        """Set the current channel as the tweet notification channel"""
        self.tweet_channel_id = ctx.channel.id
        
        embed = discord.Embed(
            title="✅ Channel Set",
            description=f"Tweet notifications will be sent to {ctx.channel.mention}",
            color=0x00ff00
        )
        await ctx.send(embed=embed)

# Configuration
BOT_TOKEN = "YOUR_DISCORD_BOT_TOKEN_HERE"  # Replace with your bot token

# Example usage and setup instructions
if __name__ == "__main__":
    print("""
🤖 Discord Twitter Monitor Bot Setup

1. Create a Discord application at https://discord.com/developers/applications
2. Create a bot and copy the token
3. Invite the bot to your server with appropriate permissions
4. Replace BOT_TOKEN with your actual bot token
5. Install required packages: pip install discord.py selenium
6. Make sure you have ChromeDriver installed

Bot Commands:
- !setchannel - Set current channel for tweet notifications
- !monitor <username> [duration] - Start monitoring a user
- !stop <username> - Stop monitoring a user  
- !status - Show monitoring status

Example:
- !setchannel
- !monitor elonmusk 120
- !status
- !stop elonmusk
    """)
    
    if BOT_TOKEN == "YOUR_DISCORD_BOT_TOKEN_HERE":
        print("❌ Please set your Discord bot token first!")
    else:
        bot = TwitterDiscordBot()
        bot.run(BOT_TOKEN)
