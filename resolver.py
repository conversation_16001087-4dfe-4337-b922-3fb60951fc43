import requests
from typing import List, Dict, Optional
import json

class Resolver:
    def __init__(self, graphql_endpoint: str, bearer_token: str, user_by_screen_name_query: str):
        self.graphql_endpoint = graphql_endpoint
        self.bearer_token = bearer_token
        self.user_by_screen_name_query = user_by_screen_name_query
        self.handle_to_user_id_cache: Dict[str, str] = {}

    def resolve_user_ids(self, handles: List[str]) -> Dict[str, str]:
        """
        Calls the private GraphQL POST with UserByScreenNameQuery to map handles to user IDs.
        Caches handle→userId mappings.
        """
        resolved_ids: Dict[str, str] = {}
        unresolved_handles: List[str] = []

        for handle in handles:
            if handle in self.handle_to_user_id_cache:
                resolved_ids[handle] = self.handle_to_user_id_cache[handle]
            else:
                unresolved_handles.append(handle)

        if unresolved_handles:
            try:
                payload = {
                    "variables": {
                        "screen_name": unresolved_handles
                    },
                    "queryId": self.user_by_screen_name_query
                }
                headers = {
                    "Authorization": f"Bearer {self.bearer_token}",
                    "Content-Type": "application/json"
                }
                response = requests.post(self.graphql_endpoint, headers=headers, data=json.dumps(payload))
                response.raise_for_status()
                data = response.json()

                if "data" in data and "users" in data["data"]:
                    for user in data["data"]["users"]:
                        handle = user["screen_name"]
                        user_id = user["id"]
                        self.handle_to_user_id_cache[handle] = user_id
                        resolved_ids[handle] = user_id
                else:
                    print(f"Unexpected GraphQL response: {data}")

            except requests.exceptions.RequestException as e:
                print(f"Request error: {e}")
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
            except Exception as e:
                print(f"An unexpected error occurred: {e}")

        return resolved_ids

if __name__ == "__main__":
    # Use actual values from your test2.py output
    graphql_endpoint = "https://api.twitter.com/graphql"
    bearer_token = "Bearer AAAAAAAAAAAAAAAAAAAAAMupswEAAAAANC5Yk%2FHGiZmGDRV3EhXMBO3uX08%3DEwAT9YySxXZXGrYScXeoKUaeyqXQFeNVWUW4SaZUvtegCUVjIi"
    user_by_screen_name_query = "aCildIOrBqrnnMChEhRk9g"

    resolver = Resolver(graphql_endpoint, bearer_token, user_by_screen_name_query)
    handles = ["elonmusk", "billgates"]  # Replace with handles you want to track
    user_ids = resolver.resolve_user_ids(handles)
    print(f"User IDs: {user_ids}")

    # Save user IDs for use in streamer.py
    if user_ids:
        print("\nCopy these user IDs to use in streamer.py:")
        for handle, user_id in user_ids.items():
            print(f"  {handle}: {user_id}")
